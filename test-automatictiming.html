<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>快照智能工单计时器 - 测试页面</title>
    <style>
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      body {
        font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto,
          sans-serif;
        background: #f0f2f5;
        padding: 20px;
      }

      .container {
        max-width: 1200px;
        margin: 0 auto;
        background: white;
        border-radius: 8px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        overflow: hidden;
      }

      .header {
        background: #1890ff;
        color: white;
        padding: 20px;
        text-align: center;
      }

      .content {
        display: flex;
        min-height: 600px;
      }

      .sidebar {
        width: 300px;
        background: #fafafa;
        border-right: 1px solid #e8e8e8;
        padding: 20px;
      }

      .main-area {
        flex: 1;
        padding: 20px;
        position: relative;
      }

      /* Antd 按钮样式 */
      .ant-btn {
        display: inline-block;
        font-weight: 400;
        text-align: center;
        white-space: nowrap;
        background-image: none;
        border: 1px solid transparent;
        cursor: pointer;
        transition: all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
        user-select: none;
        touch-action: manipulation;
        height: 32px;
        padding: 4px 15px;
        font-size: 14px;
        border-radius: 6px;
        color: rgba(0, 0, 0, 0.85);
        background: #fff;
        border-color: #d9d9d9;
        margin: 5px;
      }

      .ant-btn:hover {
        color: #40a9ff;
        border-color: #40a9ff;
      }

      .ant-btn-primary {
        color: #fff;
        background: #1890ff;
        border-color: #1890ff;
      }

      .ant-btn-primary:hover {
        background: #40a9ff;
        border-color: #40a9ff;
      }

      .ant-btn-info {
        color: #fff;
        background: #1890ff;
        border-color: #1890ff;
      }

      .ant-btn-info:hover {
        background: #40a9ff;
        border-color: #40a9ff;
      }

      /* 滚动区域 */
      #scrollBox {
        height: 400px;
        overflow-y: auto;
        border: 1px solid #e8e8e8;
        border-radius: 6px;
        padding: 10px;
        margin: 20px 0;
      }

      /* 工单卡片样式 */
      .card {
        background: white;
        border: 1px solid #e8e8e8;
        border-radius: 6px;
        padding: 16px;
        margin: 10px 0;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        transition: box-shadow 0.3s;
      }

      .card:hover {
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
      }

      .card-title {
        font-weight: bold;
        color: #1890ff;
        margin-bottom: 8px;
      }

      .card-content {
        color: #666;
        font-size: 12px;
      }

      /* 消息提示样式 */
      .el-message {
        position: fixed;
        top: 20px;
        left: 50%;
        transform: translateX(-50%);
        z-index: 10000;
        padding: 15px 20px;
        border-radius: 4px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        font-size: 14px;
        animation: slideDown 0.3s ease;
      }

      .el-message-info {
        background: #e6f7ff;
        border: 1px solid #91d5ff;
        color: #1890ff;
      }

      .el-message-error {
        background: #fff2f0;
        border: 1px solid #ffccc7;
        color: #ff4d4f;
      }

      /* 确认弹窗样式 */
      .ant-modal {
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        z-index: 10001;
        background: white;
        border-radius: 8px;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
        min-width: 400px;
        display: none;
      }

      .ant-modal-content {
        padding: 20px;
      }

      .ant-modal-header {
        border-bottom: 1px solid #e8e8e8;
        padding-bottom: 10px;
        margin-bottom: 15px;
      }

      .ant-modal-title {
        font-size: 16px;
        font-weight: 600;
        color: #262626;
      }

      .ant-modal-body {
        margin: 15px 0;
        color: red;
        font-size: 14px;
      }

      .ant-modal-footer {
        border-top: 1px solid #e8e8e8;
        padding-top: 15px;
        text-align: right;
      }

      .ant-modal-footer button {
        margin-left: 8px;
        padding: 6px 15px;
        border-radius: 6px;
        cursor: pointer;
        font-size: 14px;
        border: 1px solid #d9d9d9;
      }

      .ant-modal-footer .ant-btn-primary {
        background: #1890ff;
        color: white;
        border-color: #1890ff;
      }

      .ant-modal-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.5);
        z-index: 10000;
        display: none;
      }

      @keyframes slideDown {
        from {
          opacity: 0;
          transform: translateX(-50%) translateY(-20px);
        }
        to {
          opacity: 1;
          transform: translateX(-50%) translateY(0);
        }
      }

      /* 测试控制面板 */
      .test-panel {
        background: #f6f8fa;
        border: 1px solid #e1e4e8;
        border-radius: 6px;
        padding: 15px;
        margin-bottom: 20px;
      }

      .test-panel h3 {
        margin-bottom: 10px;
        color: #24292e;
      }

      .test-btn {
        background: #28a745;
        color: white;
        border: none;
        padding: 6px 12px;
        border-radius: 4px;
        cursor: pointer;
        margin: 3px;
        font-size: 12px;
      }

      .test-btn:hover {
        background: #218838;
      }

      .test-btn.danger {
        background: #dc3545;
      }

      .test-btn.danger:hover {
        background: #c82333;
      }

      .status-info {
        background: #e7f3ff;
        border: 1px solid #b3d8ff;
        border-radius: 4px;
        padding: 10px;
        margin: 10px 0;
        font-size: 12px;
      }

      .button-group {
        margin: 20px 0;
        text-align: center;
      }

      .scroll-hint {
        text-align: center;
        color: #999;
        padding: 20px;
        font-style: italic;
      }
    </style>
  </head>
  <body>
    <div class="container">
      <div class="header">
        <h1>🔧 快照智能工单计时器测试页面</h1>
        <p>模拟真实环境，测试插件的所有功能</p>
      </div>

      <div class="content">
        <div class="sidebar">
          <div class="test-panel">
            <h3>🎮 测试控制面板</h3>

            <h4>按钮测试</h4>
            <button class="test-btn" onclick="testButtonClick('获 取')">
              测试获取按钮
            </button>
            <button class="test-btn" onclick="testButtonClick('关闭并获取')">
              测试关闭并获取
            </button>
            <button class="test-btn" onclick="testButtonClick('提交并获取')">
              测试提交并获取
            </button>

            <h4>错误提示测试</h4>
            <button
              class="test-btn danger"
              onclick="showMessage('暂无待审核数据', 'info')"
            >
              无数据提示
            </button>
            <button
              class="test-btn danger"
              onclick="showMessage('请先签入再开始工作吧ಠ·ಠ', 'info')"
            >
              签入提示
            </button>
            <button
              class="test-btn danger"
              onclick="showMessage('可选配置为空，请联系组长配置派单', 'error')"
            >
              配置错误
            </button>

            <h4>确认弹窗测试</h4>
            <button class="test-btn" onclick="showConfirmModalTest('是否批量提交？')">
              显示确认弹窗
            </button>
            <button class="test-btn" onclick="showConfirmModalTest('是否批量提交并获取？')">
              显示提交并获取弹窗
            </button>

            <h4>提交结果测试</h4>
            <button class="test-btn" onclick="showMessage('提交成功', 'info')">
              提交成功
            </button>
            <button class="test-btn danger" onclick="showMessage('提交失败', 'error')">
              提交失败
            </button>

            <h4>工单卡片测试</h4>
            <button class="test-btn" onclick="generateCards(1)">
              生成1张卡片
            </button>
            <button class="test-btn" onclick="generateCards(5)">
              生成5张卡片
            </button>
            <button class="test-btn" onclick="generateCards(20)">
              生成20张卡片
            </button>
            <button
              class="test-btn"
              onclick="generateCards(Math.floor(Math.random() * 200) + 1)"
            >
              随机生成
            </button>
            <button class="test-btn danger" onclick="clearCards()">
              清空卡片
            </button>

            <h4>空格键测试</h4>
            <button class="test-btn" onclick="simulateSpaceKey()">
              模拟空格键
            </button>
            <button class="test-btn" onclick="scrollToBottom()">
              滚动到底部
            </button>
          </div>

          <div class="status-info">
            <strong>📊 测试状态</strong><br />
            <span id="cardCount">当前卡片数: 0</span><br />
            <span id="scrollStatus">滚动状态: 未到底部</span><br />
            <span id="lastAction">最后操作: 无</span>
          </div>

          <div class="status-info">
            <strong>💡 使用说明</strong><br />
            1. 安装插件后刷新页面<br />
            2. 使用控制面板测试各功能<br />
            3. 按空格键测试快捷键<br />
            4. 观察右上角计时器状态<br />
            5. 通过菜单打开设置面板
          </div>
        </div>

        <div class="main-area">
          <div class="button-group">
            <button class="ant-btn ant-btn-info">获 取</button>
            <button class="ant-btn ant-btn-primary">关闭并获取</button>
            <button class="ant-btn ant-btn-info">提交并获取</button>
          </div>

          <div id="scrollBox">
            <div class="scroll-hint">
              📋 工单显示区域<br />
              使用左侧控制面板生成测试卡片<br />
              滚动到底部后按空格键测试
            </div>
          </div>

          <div style="text-align: center; margin-top: 20px; color: #999">
            <p>🎯 测试提示：滚动到底部后按空格键，或点击上方按钮触发获取操作</p>
          </div>
        </div>
      </div>
    </div>

    <!-- 确认弹窗 -->
    <div class="ant-modal-overlay" id="modal-overlay"></div>
    <div class="ant-modal" id="confirm-modal">
      <div class="ant-modal-content">
        <div class="ant-modal-header">
          <div class="ant-modal-title">确认操作</div>
        </div>
        <div class="ant-modal-body" id="modal-body-text">
          是否批量提交？
        </div>
        <div class="ant-modal-footer">
          <button onclick="cancelConfirm()">取消</button>
          <button class="ant-btn-primary" onclick="confirmSubmit()">确认</button>
        </div>
      </div>
    </div>

    <script>
      // 模拟消息系统
      window.$message = {
        info: function (text, duration = 3) {
          showMessage(text, "info", duration);
        },
        error: function (text, duration = 3) {
          showMessage(text, "error", duration);
        },
      };

      // 显示消息
      function showMessage(text, type = "info", duration = 3) {
        // 移除现有消息
        const existing = document.querySelector(".el-message");
        if (existing) {
          existing.remove();
        }

        const message = document.createElement("div");
        message.className = `el-message el-message-${type}`;
        message.innerHTML = `<div class="el-message__content">${text}</div>`;

        document.body.appendChild(message);

        setTimeout(() => {
          if (message.parentNode) {
            message.remove();
          }
        }, duration * 1000);

        updateStatus("lastAction", `显示${type}消息: ${text}`);
      }

      // 生成工单卡片
      function generateCards(count) {
        const scrollBox = document.getElementById("scrollBox");

        // 清空现有内容
        scrollBox.innerHTML = "";

        for (let i = 1; i <= count; i++) {
          const card = document.createElement("div");
          card.className = "card";
          card.innerHTML = `
                    <div class="card-title">工单 #${String(i).padStart(
                      4,
                      "0"
                    )}</div>
                    <div class="card-content">
                        快照ID: snapshot_${Date.now()}_${i}<br>
                        创建时间: ${new Date().toLocaleString()}<br>
                        状态: 待审核
                    </div>
                `;
          scrollBox.appendChild(card);
        }

        updateStatus("cardCount", `当前卡片数: ${count}`);
        updateStatus("lastAction", `生成了 ${count} 张工单卡片`);

        // 延迟更新滚动状态
        setTimeout(updateScrollStatus, 100);
      }

      // 清空卡片
      function clearCards() {
        const scrollBox = document.getElementById("scrollBox");
        scrollBox.innerHTML =
          '<div class="scroll-hint">📋 工单显示区域<br>使用左侧控制面板生成测试卡片<br>滚动到底部后按空格键测试</div>';
        updateStatus("cardCount", "当前卡片数: 0");
        updateStatus("lastAction", "清空了所有卡片");
        updateScrollStatus();
      }

      // 测试按钮点击
      function testButtonClick(buttonText) {
        const buttons = document.querySelectorAll(".ant-btn");
        for (const btn of buttons) {
          if (btn.textContent.trim() === buttonText) {
            btn.click();
            updateStatus("lastAction", `点击了 "${buttonText}" 按钮`);

            // 模拟按钮点击后的确认弹窗
            setTimeout(() => {
              if (Math.random() > 0.3) { // 70% 概率显示确认弹窗
                const confirmText = buttonText.includes("获取") ? "是否批量提交并获取？" : "是否批量提交？";
                showConfirmModalTest(confirmText);
              } else {
                // 30% 概率直接显示错误
                const errors = [
                  "暂无待审核数据",
                  "请先签入再开始工作吧ಠ·ಠ",
                  "可选配置为空，请联系组长配置派单",
                ];
                const randomError = errors[Math.floor(Math.random() * errors.length)];
                const messageType = randomError.includes("配置") ? "error" : "info";
                showMessage(randomError, messageType);
              }
            }, 800);
            break;
          }
        }
      }

      // 模拟空格键
      function simulateSpaceKey() {
        const event = new KeyboardEvent("keydown", {
          code: "Space",
          key: " ",
          bubbles: true,
        });
        document.dispatchEvent(event);
        updateStatus("lastAction", "模拟按下空格键");
      }

      // 滚动到底部
      function scrollToBottom() {
        const scrollBox = document.getElementById("scrollBox");
        scrollBox.scrollTop = scrollBox.scrollHeight;
        setTimeout(updateScrollStatus, 100);
        updateStatus("lastAction", "滚动到底部");
      }

      // 更新滚动状态
      function updateScrollStatus() {
        const scrollBox = document.getElementById("scrollBox");
        const isBottom =
          scrollBox.scrollTop + scrollBox.clientHeight >=
          scrollBox.scrollHeight - 5;
        updateStatus(
          "scrollStatus",
          `滚动状态: ${isBottom ? "已到底部" : "未到底部"}`
        );
      }

      // 更新状态显示
      function updateStatus(id, text) {
        const element = document.getElementById(id);
        if (element) {
          element.textContent = text;
        }
      }

      // 显示确认弹窗
      function showConfirmModalTest(text) {
        const overlay = document.getElementById('modal-overlay');
        const modal = document.getElementById('confirm-modal');
        const bodyText = document.getElementById('modal-body-text');

        bodyText.textContent = text;
        overlay.style.display = 'block';
        modal.style.display = 'block';

        updateStatus('lastAction', `显示确认弹窗: ${text}`);
      }

      // 确认提交
      function confirmSubmit() {
        const overlay = document.getElementById('modal-overlay');
        const modal = document.getElementById('confirm-modal');

        overlay.style.display = 'none';
        modal.style.display = 'none';

        updateStatus('lastAction', '用户点击了确认按钮');

        // 模拟提交过程
        setTimeout(() => {
          // 70% 概率成功，30% 概率失败
          if (Math.random() > 0.3) {
            showMessage('提交成功', 'info');
            // 成功后生成卡片
            setTimeout(() => {
              const cardCount = Math.floor(Math.random() * 20) + 1;
              generateCards(cardCount);
            }, 1000);
          } else {
            showMessage('提交失败', 'error');
          }
        }, 1500); // 模拟网络延迟
      }

      // 取消确认
      function cancelConfirm() {
        const overlay = document.getElementById('modal-overlay');
        const modal = document.getElementById('confirm-modal');

        overlay.style.display = 'none';
        modal.style.display = 'none';

        updateStatus('lastAction', '用户取消了确认');
      }

      // 模拟原网站的空格键逻辑
      let showConfirmModal = false;
      let requireNew = false;

      function isScrollBottom() {
        const scrollBox = document.getElementById("scrollBox");
        const e = scrollBox.scrollTop;
        const t = scrollBox.clientHeight;
        const n = scrollBox.scrollHeight;
        return e + t >= n - 5; // 5px 容差
      }

      function SubmitBatchCheck(fromSpace) {
        console.log("SubmitBatchCheck called, fromSpace:", fromSpace);

        // 模拟原网站逻辑
        if (Math.random() > 0.7) {
          // 30% 概率显示错误
          const errors = [
            "暂无待审核数据",
            "请先签入再开始工作吧ಠ·ಠ",
            "可选配置为空，请联系组长配置派单",
          ];
          const randomError = errors[Math.floor(Math.random() * errors.length)];
          const messageType = randomError.includes("配置") ? "error" : "info";
          showMessage(randomError, messageType);
          return;
        }

        // 成功情况：显示确认弹窗
        const confirmText = fromSpace ? "是否批量提交并获取？" : "是否批量提交？";
        showConfirmModalTest(confirmText);
      }

      // 原网站的空格键处理逻辑
      function HotKeyEvent(e) {
        if (e.code === "Space") {
          const cards = document.querySelectorAll(".card");
          const hasCards = cards.length > 0;

          if (hasCards) {
            // 有工单时需要滚动到底部
            if (isScrollBottom()) {
              if (!showConfirmModal) {
                requireNew = true;
                SubmitBatchCheck(true);
              }
            }
          } else {
            // 无工单时直接获取
            SubmitBatchCheck(false);
          }
        }
      }

      // 事件监听
      document.addEventListener("keydown", HotKeyEvent);
      document
        .getElementById("scrollBox")
        .addEventListener("scroll", updateScrollStatus);

      // 初始化
      updateScrollStatus();
      console.log("测试页面已加载，等待插件初始化...");

      // 添加页面加载完成提示
      window.addEventListener("load", function () {
        setTimeout(() => {
          showMessage(
            "🎉 测试页面加载完成！请确保已安装 Automatictiming.user.js 插件",
            "info",
            5
          );
        }, 1000);
      });

      // 添加调试信息
      window.testPageInfo = {
        version: "1.0",
        features: [
          "✅ 模拟 Antd 按钮样式和行为",
          "✅ 模拟 $message 消息系统",
          "✅ 动态生成 .card 工单元素",
          "✅ 模拟原网站空格键逻辑",
          "✅ 滚动检测和状态显示",
          "✅ 完整的测试控制面板",
        ],
        testScenarios: [
          "1. 点击按钮 → 确认弹窗 → 提交成功 → 开始计时",
          "2. 空格键 → 确认弹窗 → 提交成功 → 开始计时",
          "3. 错误提示阻止计时（无数据、未签入、配置错误）",
          "4. 提交失败后回到就绪状态",
          "5. 卡片生成触发计时重置",
          "6. 防重复点击保护",
          "7. 超时处理机制",
          "8. 自定义时间设置",
        ],
      };

      console.log("🔧 测试页面信息:", window.testPageInfo);
    </script>
  </body>
</html>

<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>OBMP语音审核量统计工具 - 通知测试</title>
    <style>
      body {
        font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto,
          sans-serif;
        margin: 0;
        padding: 20px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: 100vh;
        color: #333;
      }

      .container {
        max-width: 800px;
        margin: 0 auto;
        background: white;
        border-radius: 12px;
        padding: 30px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
      }

      h1 {
        text-align: center;
        color: #2d3748;
        margin-bottom: 10px;
        font-size: 28px;
      }

      .subtitle {
        text-align: center;
        color: #718096;
        margin-bottom: 40px;
        font-size: 16px;
      }

      .test-section {
        margin-bottom: 30px;
        padding: 20px;
        background: #f7fafc;
        border-radius: 8px;
        border-left: 4px solid #9f7aea;
      }

      .test-section h3 {
        margin-top: 0;
        color: #2d3748;
        font-size: 18px;
      }

      .button-group {
        display: flex;
        gap: 15px;
        flex-wrap: wrap;
        margin-top: 15px;
      }

      .test-btn {
        padding: 12px 24px;
        border: none;
        border-radius: 6px;
        cursor: pointer;
        font-size: 14px;
        font-weight: 500;
        transition: all 0.2s ease;
        min-width: 120px;
      }

      .btn-success {
        background: rgba(16, 185, 129, 0.9);
        color: white;
      }

      .btn-warning {
        background: rgba(245, 158, 11, 0.9);
        color: white;
      }

      .btn-error {
        background: rgba(234, 88, 12, 0.9);
        color: white;
      }

      .btn-info {
        background: #9f7aea;
        color: white;
      }

      .test-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
      }

      .description {
        color: #4a5568;
        font-size: 14px;
        line-height: 1.5;
        margin-bottom: 15px;
      }

      .clear-btn {
        background: #6b7280;
        color: white;
        padding: 8px 16px;
        border: none;
        border-radius: 4px;
        cursor: pointer;
        font-size: 12px;
        margin-top: 20px;
      }

      .clear-btn:hover {
        background: #4b5563;
      }

      .footer {
        text-align: center;
        margin-top: 40px;
        padding-top: 20px;
        border-top: 1px solid #e2e8f0;
        color: #718096;
        font-size: 14px;
      }

      .voice-badge {
        display: inline-block;
        background: #9f7aea;
        color: white;
        padding: 4px 8px;
        border-radius: 12px;
        font-size: 12px;
        font-weight: 600;
        margin-left: 8px;
      }
    </style>
  </head>
  <body>
    <div class="container">
      <h1>🎤 OBMP语音审核量统计工具<span class="voice-badge">VOICE</span></h1>
      <p class="subtitle">通知系统测试页面</p>

      <div class="test-section">
        <h3>📊 基础通知类型</h3>
        <p class="description">
          测试不同类型的通知消息，包括成功、警告和错误状态。注意语音版本使用半透明背景。
        </p>
        <div class="button-group">
          <button
            class="test-btn btn-success"
            onclick="_showVoiceNotification('✅ 语音数据统计完成', 'success')"
          >
            成功通知
          </button>
          <button
            class="test-btn btn-warning"
            onclick="_showVoiceNotification('⚠️ 语音数据可能不准确', 'warning')"
          >
            警告通知
          </button>
          <button
            class="test-btn btn-error"
            onclick="_showVoiceNotification('❌ 语音统计失败，请重试', 'error')"
          >
            错误通知
          </button>
        </div>
      </div>

      <div class="test-section">
        <h3>🎵 语音审核相关通知</h3>
        <p class="description">模拟语音审核场景中的统计相关通知消息。</p>
        <div class="button-group">
          <button
            class="test-btn btn-success"
            onclick="_showVoiceNotification('已统计: 18条语音', 'success')"
          >
            语音统计完成
          </button>
          <button
            class="test-btn btn-success"
            onclick="_showVoiceNotification('✅ 语音数据已自动重置', 'success')"
          >
            自动重置
          </button>
          <button
            class="test-btn btn-warning"
            onclick="_showVoiceNotification('⚠️ 无法获取语音数据，请重新尝试', 'warning')"
          >
            获取失败
          </button>
          <button
            class="test-btn btn-error"
            onclick="_showVoiceNotification('⚠️ 没有找到有效的语音数据行', 'error')"
          >
            数据异常
          </button>
        </div>
      </div>

      <div class="test-section">
        <h3>🕒 时间相关通知</h3>
        <p class="description">测试日期变更和时间相关的通知消息。</p>
        <div class="button-group">
          <button
            class="test-btn btn-info"
            onclick="_showVoiceNotification('✅ 检测到日期变更，语音数据已自动重置', 'success')"
          >
            日期变更
          </button>
          <button
            class="test-btn btn-info"
            onclick="_showVoiceNotification('今日语音统计: 审核 22条，通过 18条', 'success')"
          >
            今日统计
          </button>
          <button
            class="test-btn btn-warning"
            onclick="_showVoiceNotification('❌ 准备语音数据时发生错误', 'warning')"
          >
            数据准备错误
          </button>
        </div>
      </div>

      <div class="test-section">
        <h3>📝 长文本通知</h3>
        <p class="description">
          测试不同长度的通知文本显示效果，观察语音版本的简洁布局。
        </p>
        <div class="button-group">
          <button
            class="test-btn btn-success"
            onclick="_showVoiceNotification('短文本', 'success')"
          >
            短文本
          </button>
          <button
            class="test-btn btn-warning"
            onclick="_showVoiceNotification('这是一个中等长度的语音通知消息', 'warning')"
          >
            中等文本
          </button>
          <button
            class="test-btn btn-error"
            onclick="_showVoiceNotification('这是一个非常长的语音通知消息，用来测试语音版本通知系统在处理长文本时的显示效果', 'error')"
          >
            长文本
          </button>
        </div>
      </div>

      <div class="test-section">
        <h3>🔄 批量通知测试</h3>
        <p class="description">
          测试多个通知同时显示时的效果，语音版本通知会自动管理位置。
        </p>
        <div class="button-group">
          <button
            class="test-btn btn-info"
            onclick="showMultipleNotifications()"
          >
            显示多个通知
          </button>
          <button
            class="test-btn btn-info"
            onclick="showSequentialNotifications()"
          >
            连续通知
          </button>
          <button class="clear-btn" onclick="clearAllNotifications()">
            清除所有通知
          </button>
        </div>
      </div>

      <div class="test-section">
        <h3>🎯 语音特色功能</h3>
        <p class="description">展示语音版本特有的通知场景和功能。</p>
        <div class="button-group">
          <button
            class="test-btn btn-success"
            onclick="_showVoiceNotification('🎤 语音播放完成', 'success')"
          >
            播放完成
          </button>
          <button
            class="test-btn btn-warning"
            onclick="_showVoiceNotification('🔊 音频质量检测异常', 'warning')"
          >
            音频异常
          </button>
          <button
            class="test-btn btn-error"
            onclick="_showVoiceNotification('🚫 语音文件加载失败', 'error')"
          >
            加载失败
          </button>
          <button
            class="test-btn btn-info"
            onclick="_showVoiceNotification('📊 语音时长: 2分35秒', 'success')"
          >
            时长统计
          </button>
        </div>
      </div>

      <div class="footer">
        <p>
          💡 提示：语音版本通知显示在右下角，使用半透明背景，5秒后自动消失。
        </p>
      </div>
    </div>

    <script>
      // 通知管理器 - 语音版本
      class EnhancedVoiceNotificationManager {
        constructor() {
          this.notifications = [];
          this.maxNotifications = 5;
          this.notificationId = 0;
          this.typeConfigs = {
            success: {
              color: "linear-gradient(135deg, #4CAF50, #45a049)",
              duration: 3000,
            },
            error: {
              color: "linear-gradient(135deg, #f44336, #d32f2f)",
              duration: 5000,
            },
            warning: {
              color: "linear-gradient(135deg, #ff9800, #f57c00)",
              duration: 4000,
            },
            info: {
              color: "linear-gradient(135deg, #2196F3, #1976d2)",
              duration: 3000,
            },
          };
        }

        add(message, type = "info", duration = null) {
          try {
            const config = this.typeConfigs[type] || this.typeConfigs.info;
            const finalDuration = duration || config.duration;

            const id = ++this.notificationId;
            const notification = {
              id,
              message,
              type,
              duration: finalDuration,
              config,
              element: null,
            };

            // 限制最大数量，移除最旧的
            if (this.notifications.length >= this.maxNotifications) {
              this.removeOldest();
            }

            this.notifications.push(notification);
            this.createNotificationElement(notification);
            this.updatePositions();

            // 自动移除
            setTimeout(() => {
              this.remove(id);
            }, finalDuration);

            return id;
          } catch (error) {
            console.error("添加通知失败:", error);
            return null;
          }
        }

        createNotificationElement(notification) {
          try {
            const element = document.createElement("div");
            element.className = "voice-notification";
            element.id = `voice-notification-${notification.id}`;

            // 创建通知内容容器
            const content = document.createElement("div");
            content.style.cssText = `
              display: flex;
              align-items: center;
              gap: 10px;
            `;

            // 添加消息文本
            const messageText = document.createElement("span");
            messageText.textContent = notification.message;
            messageText.style.cssText = `
              flex: 1;
              line-height: 1.4;
            `;

            // 添加关闭按钮
            const closeBtn = document.createElement("span");
            closeBtn.innerHTML = "×";
            closeBtn.style.cssText = `
              cursor: pointer;
              font-size: 18px;
              font-weight: bold;
              opacity: 0.7;
              flex-shrink: 0;
              transition: opacity 0.2s;
              padding: 2px 4px;
              border-radius: 2px;
            `;
            closeBtn.onmouseover = () => {
              closeBtn.style.opacity = "1";
              closeBtn.style.backgroundColor = "rgba(255,255,255,0.2)";
            };
            closeBtn.onmouseout = () => {
              closeBtn.style.opacity = "0.7";
              closeBtn.style.backgroundColor = "transparent";
            };
            closeBtn.onclick = (e) => {
              e.stopPropagation();
              this.remove(notification.id);
            };

            content.appendChild(messageText);
            content.appendChild(closeBtn);
            element.appendChild(content);

            element.style.cssText = `
              position: fixed;
              right: -400px;
              background: ${notification.config.color};
              color: white;
              padding: 14px 18px;
              border-radius: 8px;
              box-shadow: 0 6px 20px rgba(0,0,0,0.2);
              z-index: 10000;
              font-size: 14px;
              font-weight: 500;
              transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
              max-width: 420px;
              word-wrap: break-word;
              cursor: pointer;
              backdrop-filter: blur(10px);
              border: 1px solid rgba(255,255,255,0.1);
            `;

            // 悬停效果
            element.onmouseover = () => {
              element.style.transform = "translateX(-5px) scale(1.02)";
              element.style.boxShadow = "0 8px 25px rgba(0,0,0,0.3)";
            };
            element.onmouseout = () => {
              element.style.transform = "translateX(0) scale(1)";
              element.style.boxShadow = "0 6px 20px rgba(0,0,0,0.2)";
            };

            // 点击通知关闭
            element.onclick = () => this.remove(notification.id);

            notification.element = element;
            document.body.appendChild(element);

            // 滑入动画
            setTimeout(() => {
              element.style.right = "20px";
            }, 10);
          } catch (error) {
            console.error("创建通知元素失败:", error);
          }
        }

        remove(id) {
          const notification = this.notifications.find((n) => n.id === id);
          if (notification && notification.element) {
            notification.element.style.opacity = "0";
            notification.element.style.transform = "translateX(100%)";
            setTimeout(() => {
              if (notification.element && notification.element.parentNode) {
                notification.element.remove();
              }
              const index = this.notifications.indexOf(notification);
              if (index > -1) {
                this.notifications.splice(index, 1);
                this.updatePositions();
              }
            }, 300);
          }
        }

        updatePositions() {
          try {
            let bottomOffset = 20;
            this.notifications.forEach((notification) => {
              if (notification.element && notification.element.parentNode) {
                notification.element.style.bottom = `${bottomOffset}px`;
                bottomOffset += notification.element.offsetHeight + 12;
              }
            });
          } catch (error) {
            console.error("更新通知位置失败:", error);
          }
        }

        removeOldest() {
          if (this.notifications.length > 0) {
            this.remove(this.notifications[0].id);
          }
        }

        clear() {
          this.notifications.forEach((notification) => {
            if (notification.element && notification.element.parentNode) {
              notification.element.remove();
            }
          });
          this.notifications.length = 0;
        }
      }

      // 创建全局通知管理器实例
      const notificationManager = new EnhancedVoiceNotificationManager();

      // 增强的通知显示包装器
      function _showVoiceNotification(message, type = "info", duration = null) {
        try {
          return notificationManager.add(message, type, duration);
        } catch (error) {
          console.error("显示语音通知失败:", error);
          const typeConfig = {
            success: { title: "成功" },
            error: { title: "错误" },
            warning: { title: "警告" },
            info: { title: "信息" },
          };
          const config = typeConfig[type] || typeConfig.info;
          console.log(`[语音通知-${config.title}] ${message}`);
          return null;
        }
      }

      // 显示通知 - 语音版本样式
      function showNotification(message, type = "success") {
        return notificationManager.add(message, type);
      }

      // 显示多个通知
      function showMultipleNotifications() {
        _showVoiceNotification("🎤 第一个语音通知", "success");
        setTimeout(
          () => _showVoiceNotification("🔊 第二个语音通知", "warning"),
          200
        );
        setTimeout(
          () => _showVoiceNotification("📊 第三个语音通知", "error"),
          400
        );
      }

      // 连续通知
      function showSequentialNotifications() {
        const messages = [
          { text: "🎵 开始处理语音数据...", type: "success" },
          { text: "🔍 正在验证音频质量...", type: "warning" },
          { text: "✅ 语音数据处理完成!", type: "success" },
        ];

        messages.forEach((msg, index) => {
          setTimeout(() => {
            _showVoiceNotification(msg.text, msg.type);
          }, index * 800);
        });
      }

      // 清除所有通知
      function clearAllNotifications() {
        notificationManager.clear();
      }

      // 页面加载完成后显示欢迎通知
      window.addEventListener("load", () => {
        setTimeout(() => {
          _showVoiceNotification("🎉 语音通知测试页面加载完成", "success");
        }, 500);
      });
    </script>
  </body>
</html>

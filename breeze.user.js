// ==UserScript==
// @name         清风平台自动获取工单
// @namespace    http://tampermonkey.net/
// @version      3.2
// @description  简洁高效的连点器，专为清风审核平台优化，稳定可靠的工单获取工具
// <AUTHOR>
// @match        https://breeze.opd.netease.com/center/workbench
// @icon         data:image/svg+xml;base64,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
// @updateURL    https://gengxin.geluman.cn/OBMP/breeze.user.js
// @downloadURL  https://gengxin.geluman.cn/OBMP/breeze.user.js
// @grant        GM_getValue
// @grant        GM_setValue
// @grant        GM_registerMenuCommand
// ==/UserScript==

(function () {
  "use strict";

  // 默认配置（会从存储加载用户设置）
  const DEFAULT_CONFIG = {
    hotkey: "Alt+Shift+Q",
    clickInterval: 200, // 连点间隔（毫秒）
    autoHideUI: true,
    notification: true,
    advancedMode: false,
    errorMonitoring: true,
    monitoredErrorCodes: [10212],
  };

  // 状态管理
  const state = {
    isRunning: false,
    isUIVisible: true,
    intervalId: null,
    lastButton: null,
  };

  // 加载用户配置
  function loadConfig() {
    return {
      ...DEFAULT_CONFIG,
      ...GM_getValue("clickerConfig", {}),
    };
  }

  // 当前配置
  let config = loadConfig();

  // 主功能实现 --------------------------------------------------------

  // 智能查找目标按钮
  function findTargetButton() {
    // 辅助函数：检查是否为插件自身按钮
    function isPluginButton(button) {
      return (
        button.id === "clicker-toggle" ||
        button.id === "clicker-settings" ||
        button.id === "clicker-hide" ||
        button.closest("#clicker-ui")
      );
    }

    // 方案1: 精确匹配Ant Design主按钮结构
    const primaryButtons = document.querySelectorAll("button.ant-btn-primary");
    for (const button of primaryButtons) {
      if (isPluginButton(button)) continue; // 排除插件按钮
      const spans = button.querySelectorAll("span");
      for (const span of spans) {
        if (/Acquire\s*\(Alt\+A\)|Acquire/i.test(span.textContent)) {
          return button;
        }
      }
    }

    // 方案2: 通过类名组合匹配
    const antButtons = document.querySelectorAll(
      "button.ant-btn.ant-btn-primary.ant-btn-color-primary"
    );
    for (const button of antButtons) {
      if (isPluginButton(button)) continue; // 排除插件按钮
      if (/Acquire|获取/i.test(button.textContent)) {
        return button;
      }
    }

    // 方案3: 兼容性模糊匹配
    const allButtons = document.querySelectorAll("button");
    for (const button of allButtons) {
      if (isPluginButton(button)) continue; // 排除插件按钮
      const spans = button.querySelectorAll("span");
      for (const span of spans) {
        if (/Acquire|获取/i.test(span.textContent)) {
          return button;
        }
      }
    }

    // 方案4: 最后备用匹配
    for (const button of allButtons) {
      if (isPluginButton(button)) continue; // 排除插件按钮
      if (/Acquire|获取/i.test(button.textContent)) {
        return button;
      }
    }

    return null;
  }

  // 控制核心 - 连点模式
  function startClicking() {
    if (state.isRunning) return;

    const button = findTargetButton();
    if (!button) {
      showNotification("未找到目标按钮，请确认页面已加载完成", "orange");
      console.log("[连点器] 未找到目标按钮，可能的原因：");
      console.log("1. 页面尚未完全加载");
      console.log("2. 按钮结构发生变化");
      console.log("3. 当前页面不包含Acquire按钮");
      return;
    }

    state.isRunning = true;
    state.lastButton = button;

    // 连点模式 - 稳定可靠的实现
    state.intervalId = setInterval(() => {
      const btn = findTargetButton() || state.lastButton;
      if (!btn || btn.disabled) {
        if (config.advancedMode) {
          stopClicking();
          showNotification("⚠️ 按钮不可用，已自动停止", "orange");
        }
        return;
      }
      try {
        btn.click();
        state.lastButton = btn;
      } catch (e) {
        console.error("[连点器] 点击错误:", e);
        if (config.advancedMode) {
          stopClicking();
          showNotification("⚠️ 点击失败，已自动停止", "orange");
        }
      }
    }, config.clickInterval);

    console.log(`[连点器] 启动成功，目标按钮:`, button);
    console.log(`[连点器] 点击间隔: ${config.clickInterval}ms`);

    updateUI();
    showNotification("✅ 连点器启动", "green");
  }

  function stopClicking() {
    if (!state.isRunning) return;

    clearInterval(state.intervalId);
    state.isRunning = false;

    console.log("[连点器] 已停止");
    updateUI();
    showNotification("⏹️ 连点器停止", "#666");
  }

  function toggleClicking() {
    state.isRunning ? stopClicking() : startClicking();
  }

  // UI 管理系统 ------------------------------------------------------

  // 创建浮动控制面板
  function createMainUI() {
    const ui = document.createElement("div");
    ui.id = "clicker-main-ui";
    ui.style.cssText = `
            position: fixed;
            bottom: 20px;
            left: 20px;
            z-index: 9999;
            background: rgba(30,30,30,0.95);
            border: 1px solid #444;
            border-radius: 8px;
            padding: 12px;
            color: white;
            font-family: Arial, sans-serif;
            min-width: 220px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.3);
            backdrop-filter: blur(5px);
            transition: transform 0.3s ease;
        `;

    ui.innerHTML = `
            <div style="display:flex; justify-content:space-between; align-items:center; margin-bottom:10px;">
                <div style="font-weight:bold;">🖱️ 清风工单获取器</div>
                <div id="clicker-minimize" style="cursor:pointer; padding:0 5px;">−</div>
            </div>
            <div id="clicker-status" style="margin:8px 0; font-size:14px;">
                ● 状态: <span style="color:${
                  state.isRunning ? "#6fde6f" : "#ff6b6b"
                }">${state.isRunning ? "运行中" : "已停止"}</span>
            </div>
            <button id="clicker-toggle" style="
                width:100%; padding:8px; margin:5px 0;
                background:${state.isRunning ? "#e74c3c" : "#2ecc71"};
                color:white; border:none; border-radius:4px;
                cursor:pointer; font-weight:bold;">
                ${state.isRunning ? "⏹ 停止" : "▶ 开始"} (${config.hotkey})
            </button>
            <div style="display:flex; justify-content:space-between; margin-top:10px;">
                <button id="clicker-settings" style="
                    padding:5px 10px; background:#3498db;
                    color:white; border:none; border-radius:3px; cursor:pointer;">
                    ⚙️ 设置
                </button>
                <button id="clicker-hide" style="
                    padding:5px 10px; background:#555;
                    color:white; border:none; border-radius:3px; cursor:pointer;">
                    ✕ 隐藏
                </button>
            </div>
        `;

    // 添加事件监听
    ui.querySelector("#clicker-toggle").addEventListener(
      "click",
      toggleClicking
    );
    ui.querySelector("#clicker-settings").addEventListener(
      "click",
      showSettingsPanel
    );
    ui.querySelector("#clicker-hide").addEventListener("click", () =>
      toggleUIVisibility(false)
    );
    ui.querySelector("#clicker-minimize").addEventListener("click", () =>
      toggleUIVisibility(false)
    );

    document.body.appendChild(ui);

    // 初始隐藏处理
    if (config.autoHideUI && !state.isRunning) {
      setTimeout(() => toggleUIVisibility(false), 2000);
    }
  }

  // 设置面板
  function showSettingsPanel() {
    if (document.getElementById("clicker-settings-ui")) return;
    toggleUIVisibility(true);

    const settingsUI = document.createElement("div");
    settingsUI.id = "clicker-settings-ui";
    settingsUI.style.cssText = `
            position: fixed;
            bottom: 80px;
            left: 20px;
            z-index: 10000;
            background: rgba(40,40,40,0.98);
            border: 1px solid #444;
            border-radius: 8px;
            padding: 15px;
            color: white;
            font-family: Arial, sans-serif;
            width: 280px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.4);
        `;

    settingsUI.innerHTML = `
            <div style="font-weight:bold; margin-bottom:15px; border-bottom:1px solid #555; padding-bottom:8px;">
                ⚙️ 连点器设置
            </div>

            <div style="margin-bottom:12px;">
                <label style="display:block; margin-bottom:5px; font-size:14px;">
                    <input type="checkbox" id="clicker-auto-hide" ${
                      config.autoHideUI ? "checked" : ""
                    }>
                    自动隐藏控制面板
                </label>
            </div>

            <div style="margin-bottom:12px;">
                <label style="display:block; margin-bottom:5px; font-size:14px;">
                    <input type="checkbox" id="clicker-notification" ${
                      config.notification ? "checked" : ""
                    }>
                    显示操作通知
                </label>
            </div>

            <div style="margin-bottom:12px;">
                <label style="display:block; margin-bottom:5px; font-size:14px;">
                    <input type="checkbox" id="clicker-advanced" ${
                      config.advancedMode ? "checked" : ""
                    }>
                    高级模式（出错自动停止）
                </label>
            </div>

            <div style="margin-bottom:12px;">
                <label style="display:block; margin-bottom:5px; font-size:14px;">
                    <input type="checkbox" id="clicker-error-monitoring" ${
                      config.errorMonitoring ? "checked" : ""
                    }>
                    智能错误监听
                </label>
            </div>

            <div style="margin-bottom:15px;">
                <label style="display:block; margin-bottom:5px; font-size:14px;">
                    点击间隔 (ms):
                    <input type="number" id="clicker-click-interval" value="${
                      config.clickInterval
                    }" min="100" max="5000"
                        style="width:80px; padding:4px; margin-left:10px; background:#333; color:white; border:1px solid #555;">
                </label>
                <div style="font-size:12px; color:#999; margin-top:2px;">连点间隔时间，值越小点击越频繁（100-5000ms）</div>
            </div>

            <div style="display:flex; justify-content:flex-end;">
                <button id="clicker-settings-save" style="
                    padding:6px 12px; margin-right:8px;
                    background:#2ecc71; color:white;
                    border:none; border-radius:4px; cursor:pointer;">
                    保存
                </button>
                <button id="clicker-settings-close" style="
                    padding:6px 12px;
                    background:#555; color:white;
                    border:none; border-radius:4px; cursor:pointer;">
                    关闭
                </button>
            </div>
        `;

    document.body.appendChild(settingsUI);

    // 保存设置
    settingsUI
      .querySelector("#clicker-settings-save")
      .addEventListener("click", () => {
        config = {
          ...config,
          autoHideUI: document.getElementById("clicker-auto-hide").checked,
          notification: document.getElementById("clicker-notification").checked,
          advancedMode: document.getElementById("clicker-advanced").checked,
          errorMonitoring: document.getElementById("clicker-error-monitoring")
            .checked,
          clickInterval:
            parseInt(document.getElementById("clicker-click-interval").value) ||
            200,
        };

        GM_setValue("clickerConfig", config);
        showNotification("设置已保存", "#3498db");
        settingsUI.remove();
      });

    // 关闭设置
    settingsUI
      .querySelector("#clicker-settings-close")
      .addEventListener("click", () => {
        settingsUI.remove();
      });
  }

  // 通知队列管理
  const notificationQueue = [];
  let notificationIdCounter = 0;

  // 显示通知
  function showNotification(msg, color) {
    if (!config.notification) return;

    // 创建通知对象
    const notification = {
      id: ++notificationIdCounter,
      message: msg,
      color: color,
      element: null,
    };

    // 添加到队列
    notificationQueue.push(notification);

    // 创建并显示通知
    createNotificationElement(notification);

    // 重新排列所有通知位置
    repositionNotifications();

    // 设置自动移除
    setTimeout(() => {
      removeNotification(notification.id);
    }, 2500);
  }

  // 创建通知元素
  function createNotificationElement(notification) {
    // 确保CSS动画样式已添加
    addNotificationStyles();

    const notify = document.createElement("div");
    notify.id = `clicker-notification-${notification.id}`;
    notify.className = "clicker-notification";
    // 根据文本长度动态调整样式
    const isLongText = notification.message.length > 15;

    notify.style.cssText = `
            position: fixed;
            right: -400px;
            background: ${notification.color};
            color: white;
            padding: 12px 18px;
            border-radius: 6px;
            z-index: 99999;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            font-size: 14px;
            font-weight: 500;
            min-width: 200px;
            max-width: ${isLongText ? "320px" : "280px"};
            ${
              isLongText
                ? "word-wrap: break-word; line-height: 1.4; max-height: 60px; overflow: hidden;"
                : "white-space: nowrap; overflow: hidden; text-overflow: ellipsis;"
            }
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.1);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        `;
    notify.textContent = notification.message;
    notification.element = notify;

    document.body.appendChild(notify);

    // 触发入场动画
    requestAnimationFrame(() => {
      notify.style.right = "20px";
      notify.style.transform = "translateX(0) scale(1)";
      notify.style.opacity = "1";
    });
  }

  // 重新排列通知位置
  function repositionNotifications() {
    let bottomOffset = 60;

    notificationQueue.forEach((notification) => {
      if (notification.element) {
        notification.element.style.bottom = `${bottomOffset}px`;
        bottomOffset += notification.element.offsetHeight + 12;
      }
    });
  }

  // 移除通知
  function removeNotification(notificationId) {
    const index = notificationQueue.findIndex((n) => n.id === notificationId);
    if (index === -1) return;

    const notification = notificationQueue[index];
    if (notification.element) {
      // 出场动画
      notification.element.style.right = "-400px";
      notification.element.style.opacity = "0";
      notification.element.style.transform = "translateX(20px) scale(0.9)";

      setTimeout(() => {
        if (notification.element && notification.element.parentNode) {
          notification.element.remove();
        }
      }, 300);
    }

    // 从队列中移除
    notificationQueue.splice(index, 1);

    // 重新排列剩余通知
    setTimeout(() => {
      repositionNotifications();
    }, 100);
  }

  // 添加通知样式
  function addNotificationStyles() {
    if (document.getElementById("clicker-notification-styles")) return;

    const style = document.createElement("style");
    style.id = "clicker-notification-styles";
    style.textContent = `
      .clicker-notification {
        transform: translateX(20px) scale(0.9);
        opacity: 0;
      }

      .clicker-notification:hover {
        transform: translateX(-2px) scale(1.02) !important;
        box-shadow: 0 6px 20px rgba(0,0,0,0.25) !important;
      }
    `;
    document.head.appendChild(style);
  }

  // 控制台错误监听
  function setupConsoleMonitoring() {
    if (!config.errorMonitoring) return;

    const originalLog = console.log;
    const originalError = console.error;
    const originalWarn = console.warn;

    function checkForErrors(args) {
      try {
        const logContent = args
          .map((arg) => {
            if (typeof arg === "object") {
              // 检查是否为DOM元素，避免循环引用
              if (arg instanceof Element || arg instanceof Node) {
                return `[DOM Element: ${arg.tagName || "Node"}]`;
              }
              // 安全的JSON序列化，处理循环引用
              try {
                return JSON.stringify(arg);
              } catch (e) {
                return `[Object: ${Object.prototype.toString.call(arg)}]`;
              }
            }
            return String(arg);
          })
          .join(" ");

        // 检测错误码 10212 或相关错误信息
        const hasErrorCode = config.monitoredErrorCodes.some(
          (code) =>
            logContent.includes(String(code)) ||
            (typeof args[0] === "object" && args[0]?.code === code)
        );

        const hasErrorMessage =
          logContent.includes("工单已满") ||
          logContent.includes("请清理后重新获取");

        if (hasErrorCode || hasErrorMessage) {
          if (state.isRunning) {
            stopClicking();
            showNotification("🚫 检测到工单已满，已自动停止", "orange");
            console.log("[获取器] 检测到错误码，自动停止操作");
          }
        }
      } catch (e) {
        // 监听过程中出错不影响原有功能
        console.error("[获取器] 错误监听异常:", e);
      }
    }

    console.log = function (...args) {
      checkForErrors(args);
      return originalLog.apply(console, args);
    };

    console.error = function (...args) {
      checkForErrors(args);
      return originalError.apply(console, args);
    };

    console.warn = function (...args) {
      checkForErrors(args);
      return originalWarn.apply(console, args);
    };
  }

  // 更新UI状态
  function updateUI() {
    const mainUI = document.getElementById("clicker-main-ui");
    if (!mainUI) return;

    mainUI.querySelector(
      "#clicker-status"
    ).innerHTML = `● 状态: <span style="color:${
      state.isRunning ? "#6fde6f" : "#ff6b6b"
    }">${state.isRunning ? "运行中" : "已停止"}</span>`;

    const toggleBtn = mainUI.querySelector("#clicker-toggle");
    toggleBtn.textContent = `${state.isRunning ? "⏹ 停止" : "▶ 开始"} (${
      config.hotkey
    })`;
    toggleBtn.style.background = state.isRunning ? "#e74c3c" : "#2ecc71";

    // 同时更新隐藏指示器状态
    updateHideIndicator();
  }

  // 显示/隐藏UI
  function toggleUIVisibility(show) {
    const mainUI = document.getElementById("clicker-main-ui");
    if (!mainUI) return;

    state.isUIVisible = show !== undefined ? show : !state.isUIVisible;
    mainUI.style.transform = state.isUIVisible
      ? "translateX(0)"
      : "translateX(-120%)";

    // 显示小箭头提示
    if (!state.isUIVisible) {
      showHideIndicator();
    } else {
      removeHideIndicator();
    }
  }

  // 隐藏时的显示小箭头和快捷按钮
  function showHideIndicator() {
    removeHideIndicator();

    const indicator = document.createElement("div");
    indicator.id = "clicker-hide-indicator";
    indicator.style.cssText = `
            position: fixed;
            bottom: 25px;
            left: 0;
            width: 20px;
            height: 80px;
            background: rgba(30,30,30,0.7);
            border: 1px solid #444;
            border-left: none;
            border-radius: 0 4px 4px 0;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: space-around;
            color: white;
            z-index: 9998;
            padding: 2px 0;
        `;

    // 创建显示按钮
    const showBtn = document.createElement("div");
    showBtn.style.cssText = `
            width: 16px;
            height: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            font-size: 12px;
            border-radius: 2px;
            transition: background 0.2s;
        `;
    showBtn.textContent = "»";
    showBtn.title = "显示控制面板";
    showBtn.addEventListener("click", () => toggleUIVisibility(true));
    showBtn.addEventListener("mouseenter", () => {
      showBtn.style.background = "rgba(255,255,255,0.2)";
    });
    showBtn.addEventListener("mouseleave", () => {
      showBtn.style.background = "transparent";
    });

    // 创建启动/停止按钮
    const toggleBtn = document.createElement("div");
    toggleBtn.id = "clicker-hide-toggle-btn";
    toggleBtn.style.cssText = `
            width: 12px;
            height: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right:1.5px;
            cursor: pointer;
            font-size: 8px;
            border-radius: 2px;
            transition: background 0.2s;
            background: ${state.isRunning ? "#e74c3c" : "#2ecc71"};
            color: white;
            line-height: 1;
            padding: 0;
        `;
    toggleBtn.textContent = state.isRunning ? "⏹" : "▶";
    toggleBtn.title = state.isRunning ? "停止连点器" : "启动连点器";
    // 手动调整符号的视觉居中
    if (!state.isRunning) {
      // ▶符号需要向右下微调
      toggleBtn.style.transform = "translate(0.5px, 0.5px)";
    } else {
      // ⏹符号保持默认居中
      toggleBtn.style.transform = "none";
    }
    toggleBtn.addEventListener("click", (e) => {
      e.stopPropagation();
      toggleClicking();
      updateHideIndicator();
    });

    indicator.appendChild(toggleBtn);
    indicator.appendChild(showBtn);
    document.body.appendChild(indicator);
  }

  function removeHideIndicator() {
    const indicator = document.getElementById("clicker-hide-indicator");
    if (indicator) indicator.remove();
  }

  // 更新隐藏指示器状态
  function updateHideIndicator() {
    const toggleBtn = document.getElementById("clicker-hide-toggle-btn");
    if (toggleBtn) {
      toggleBtn.textContent = state.isRunning ? "⏹" : "▶";
      toggleBtn.title = state.isRunning ? "停止连点器" : "启动连点器";
      toggleBtn.style.background = state.isRunning ? "#e74c3c" : "#2ecc71";
      // 手动调整符号的视觉居中
      if (!state.isRunning) {
        // ▶符号需要向右下微调
        toggleBtn.style.transform = "translate(0.5px, 0.5px)";
      } else {
        // ⏹符号保持默认居中
        toggleBtn.style.transform = "none";
      }
    }
  }

  // 快捷键处理
  function handleHotkey(e) {
    if (e.altKey && e.shiftKey && e.key.toLowerCase() === "q" && !e.ctrlKey) {
      e.preventDefault();
      e.stopImmediatePropagation();
      toggleClicking();
    }
  }

  // 初始化
  function init() {
    // 注册插件菜单命令
    GM_registerMenuCommand("显示/隐藏获取器面板", () => toggleUIVisibility());
    GM_registerMenuCommand("打开获取器设置", showSettingsPanel);

    // 设置控制台监听
    setupConsoleMonitoring();

    // 创建UI
    createMainUI();

    // 添加快捷键
    document.addEventListener("keydown", handleHotkey, true);

    console.log("[专业获取器] 初始化完成");
  }

  // 启动初始化
  if (document.readyState === "complete") {
    init();
  } else {
    window.addEventListener("load", init);
  }
})();

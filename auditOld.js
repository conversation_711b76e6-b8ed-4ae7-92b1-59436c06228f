// ==UserScript==
// @name         OBMP审核量统计工具
// @namespace    http://tampermonkey.net/
// @version      2.9
// @description  精准统计OBMP复查数据，支持配置自动重置和历史显示
// <AUTHOR>
// @match        http://cloudweb.gameyw.netease.com:18080/u-gitlab/opd-obmp/recheck/review/index
// @icon         data:image/svg+xml;base64,PHN2ZyB0PSIxNzQ0MDU2NTE1MjQ4IiBjbGFzcz0iaWNvbiIgdmlld0JveD0iMCAwIDEwMjQgMTAyNCIgdmVyc2lvbj0iMS4xIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHAtaWQ9IjM2NzMiIHdpZHRoPSI2NCIgaGVpZ2h0PSI2NCI+PHBhdGggZD0iTTAgMG0xMDIuNCAwbDgxOS4yIDBxMTAyLjQgMCAxMDIuNCAxMDIuNGwwIDgxOS4yIHEwIDEwMi40LTEwMi40IDEwMi40bC04MTkuMiAwIHEtMTAyLjQgMC0xMDIuNC0xMDIuNGwwLTgxOS4yIHEwLTEwMi40IDEwMi40LTEwMi40WiIgZmlsbD0iIzMzODhGRiIgb3BhY2l0eT0iLjEiIHAtaWQ9IjM2NzQiPjwvcGF0aD48cGF0aCBkPSJNNTM3LjYgNjQwbTAgMjUuNmwwIDc2LjggcTAgMjUuNi0yNS42IDI1LjZsMCAwIHEtMjUuNiAwLTI1LjYtMjUuNmwwLTc2LjggcTAtMjUuNiAyNS42LTI1LjZsMCAwIHEyNS42IDAgMjUuNiAyNS42WiIgZmlsbD0iIzEyNjlGRiIgb3BhY2l0eT0iLjUiIHAtaWQ9IjM2NzUiPjwvcGF0aD48cGF0aCBkPSJNMjMwLjQgMjU2bTUxLjIgMGw0NjAuOCAwIHE1MS4yIDAgNTEuMiA1MS4ybDAgMzA3LjIgcTAgNTEuMi01MS4yIDUxLjJsLTQ2MC44IDAgcS01MS4yIDAtNTEuMi01MS4ybDAtMzA3LjIgcTAtNTEuMiA1MS4yLTUxLjJaIiBmaWxsPSIjMTI2OUZGIiBwLWlkPSIzNjc2Ij48L3BhdGg+PHBhdGggZD0iTTMwNy4yIDcxNi44bTI1LjYgMGwzNTguNCAwIHEyNS42IDAgMjUuNiAyNS42bDAgMCBxMCAyNS42LTI1LjYgMjUuNmwtMzU4LjQgMCBxLTI1LjYgMC0yNS42LTI1LjZsMCAwIHEwLTI1LjYgMjUuNi0yNS42WiIgZmlsbD0iIzEyNjlGRiIgcC1pZD0iMzY3NyI+PC9wYXRoPjxwYXRoIGQ9Ik03MjEuNDIwOCAzNjMuMjEyOGEyMy41NTIgMjMuNTUyIDAgMCAxLTIuNTk4NCAzMi43MDRsLTE5NS40MDQ4IDE3MS43NzZhMTQuNCAxNC40IDAgMCAxLTE4Ljk0NCAwLjA2NGwtOTUuNDg4LTgyLjkwNTZhMTQuNCAxNC40IDAgMCAwLTE4Ljg0MTYtMC4wMjU2bC01Ni4zNzEyIDQ4LjY0YTIyLjAxNiAyMi4wMTYgMCAwIDEtMzEuNDc1Mi0yLjc5MDQgMjMuMzM0NCAyMy4zMzQ0IDAgMCAxIDIuOTA1Ni0zMi40MDk2bDg1LjUwNC03My41MjMyYTE0LjQgMTQuNCAgMCAwIDEgMTguODI4OCAwLjAzODRsOTUuNDQ5NiA4Mi44OGM1LjQ1MjggNC43MzYgMTMuNTgwOCA0LjY5NzYgMTguOTk1Mi0wLjA4OTZsMTY1Ljc2LTE0Ni44MTZhMjIuMTA1NiAyMi4xMDU2IDAgMCAxIDMxLjY4IDIuNDU3NnoiIGZpbGw9IiNGRkZGRkYiIHAtaWQ9IjM2NzgiPjwvcGF0aD48L3N2Zz4=
// @updateURL    https://gengxin.geluman.cn/OBMP/audit.user.js
// @downloadURL  https://gengxin.geluman.cn/OBMP/audit.user.js
// @grant        GM_setValue
// @grant        GM_getValue
// @grant        GM_addStyle
// @grant        GM_notification
// ==/UserScript==

(function () {
  "use strict";

  // 添加防抖函数减少性能消耗
  function debounce(func, wait) {
    let timeout;
    return function (...args) {
      clearTimeout(timeout);
      timeout = setTimeout(() => func.apply(this, args), wait);
    };
  }

  // 数据管理类
  class StatsManager {
    constructor() {
      this.stats = this.loadStats();
      this.isProcessing = false;

      // 初始化时立即检查日期变更（处理关机重启的情况）
      this.checkDateChangeOnStartup();

      // 添加定时检查
      setInterval(() => this.checkDateChange(), 60000); // 每分钟检查一次
    }

    // 检查日期变更 - 专用于启动时检查
    checkDateChangeOnStartup() {
      // 获取当前日期，使用标准格式 YYYY-MM-DD
      const now = new Date();
      const currentYear = now.getFullYear();
      const currentMonth = String(now.getMonth() + 1).padStart(2, "0");
      const currentDay = String(now.getDate()).padStart(2, "0");
      const currentDate = `${currentYear}-${currentMonth}-${currentDay}`;

      // 获取上次更新日期，转换为标准格式
      const lastUpdated = new Date(this.stats.lastUpdated);
      const lastYear = lastUpdated.getFullYear();
      const lastMonth = String(lastUpdated.getMonth() + 1).padStart(2, "0");
      const lastDay = String(lastUpdated.getDate()).padStart(2, "0");
      const lastUpdateDate = `${lastYear}-${lastMonth}-${lastDay}`;

      // 如果上次更新日期与当前日期不同，但不是今天的数据，需要归档
      if (currentDate !== lastUpdateDate) {
        console.log(
          "检测到日期已变更（脚本重新启动）",
          lastUpdateDate,
          "->",
          currentDate
        );

        // 计算上次更新日期到现在经过了多少天
        const daysDiff = Math.floor(
          (now - lastUpdated) / (24 * 60 * 60 * 1000)
        );

        // 检查是否有需要归档的数据
        const totalCount = Object.values(this.stats.hourlyData).reduce(
          (a, b) => a + b,
          0
        );
        if (totalCount > 0) {
          // 使用标准化的日期格式 YYYY-MM-DD
          const formattedLastDate = lastUpdateDate;

          if (!this.stats.dailyData[formattedLastDate]) {
            this.stats.dailyData[formattedLastDate] = {
              total: 0,
              resetCount: 0,
            };
          }
          this.stats.dailyData[formattedLastDate].total += totalCount;
          this.stats.dailyData[formattedLastDate].resetCount++;

          // 清空当前统计
          this.stats.hourlyData = {};
          this.stats.processedBatches = [];
          this.saveStats();

          if (daysDiff === 1) {
            // 格式化通知消息中的日期为YYYY-MM-DD格式
            const formattedNotificationDate = formattedLastDate;
            this.showNotification(
              `📊 已自动归档${formattedNotificationDate}的${totalCount}条数据`,
              "success"
            );
          } else {
            this.showNotification(
              `⚠️ 检测到多天未操作，已将${totalCount}条数据归档至 ${formattedLastDate}`,
              "warning"
            );
          }
        }

        // 更新最后检查日期
        this.lastCheckedDate = currentDate;
      } else {
        this.lastCheckedDate = currentDate;
      }
    }

    // 检查日期变更 - 用于运行时检查
    checkDateChange() {
      // 获取当前日期，使用标准格式 YYYY-MM-DD
      const now = new Date();
      const currentYear = now.getFullYear();
      const currentMonth = String(now.getMonth() + 1).padStart(2, "0");
      const currentDay = String(now.getDate()).padStart(2, "0");
      const currentDate = `${currentYear}-${currentMonth}-${currentDay}`;

      if (currentDate !== this.lastCheckedDate) {
        console.log("检测到日期变更，自动归档数据");
        const totalCount = this.reset(this.lastCheckedDate);
        if (totalCount > 0) {
          this.showNotification(
            `📊 已自动归档${this.lastCheckedDate}的${totalCount}条数据`,
            "success"
          );
        }
        this.lastCheckedDate = currentDate;
      }
    }

    // 通知队列管理
    static notificationQueue = [];
    static notificationIdCounter = 0;

    // 显示通知
    showNotification(message, type = "success") {
      // 创建通知对象
      const notification = {
        id: ++StatsManager.notificationIdCounter,
        message: message,
        color:
          type === "success"
            ? "#10b981"
            : type === "warning"
            ? "#f59e0b"
            : "#ef4444",
        element: null,
      };

      // 添加到队列
      StatsManager.notificationQueue.push(notification);

      // 创建并显示通知
      this.createNotificationElement(notification);

      // 重新排列所有通知位置
      this.repositionNotifications();

      // 设置自动移除
      setTimeout(() => {
        this.removeNotification(notification.id);
      }, 2500);
    }

    // 创建通知元素
    createNotificationElement(notification) {
      // 确保CSS动画样式已添加
      this.addNotificationStyles();

      const notify = document.createElement("div");
      notify.id = `audit-notification-${notification.id}`;
      notify.className = "audit-notification";

      notify.style.cssText = `
              position: fixed;
              right: -400px;
              background: ${notification.color};
              color: white;
              padding: 12px 18px;
              border-radius: 6px;
              z-index: 99999;
              box-shadow: 0 4px 12px rgba(0,0,0,0.15);
              font-size: 14px;
              font-weight: 500;
              backdrop-filter: blur(10px);
              border: 1px solid rgba(255,255,255,0.1);
              transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
          `;
      notify.textContent = notification.message;
      notification.element = notify;

      document.body.appendChild(notify);

      // 触发入场动画
      requestAnimationFrame(() => {
        notify.style.right = "20px";
        notify.style.transform = "translateX(0) scale(1)";
        notify.style.opacity = "1";
      });
    }

    // 重新排列通知位置
    repositionNotifications() {
      let bottomOffset = 60;

      StatsManager.notificationQueue.forEach((notification) => {
        if (notification.element) {
          notification.element.style.bottom = `${bottomOffset}px`;
          bottomOffset += notification.element.offsetHeight + 12;
        }
      });
    }

    // 移除通知
    removeNotification(notificationId) {
      const index = StatsManager.notificationQueue.findIndex(
        (n) => n.id === notificationId
      );
      if (index === -1) return;

      const notification = StatsManager.notificationQueue[index];
      if (notification.element) {
        // 出场动画
        notification.element.style.right = "-400px";
        notification.element.style.opacity = "0";
        notification.element.style.transform = "translateX(20px) scale(0.9)";

        setTimeout(() => {
          if (notification.element && notification.element.parentNode) {
            notification.element.remove();
          }
        }, 300);
      }

      // 从队列中移除
      StatsManager.notificationQueue.splice(index, 1);

      // 重新排列剩余通知
      setTimeout(() => {
        this.repositionNotifications();
      }, 100);
    }

    // 添加通知样式
    addNotificationStyles() {
      if (document.getElementById("audit-notification-styles")) return;

      const style = document.createElement("style");
      style.id = "audit-notification-styles";
      style.textContent = `
        .audit-notification {
          transform: translateX(20px) scale(0.9);
          opacity: 0;
        }

        .audit-notification:hover {
          transform: translateX(-2px) scale(1.02) !important;
          box-shadow: 0 6px 20px rgba(0,0,0,0.25) !important;
        }
      `;
      document.head.appendChild(style);
    }

    // 修改reset方法，添加日期参数
    reset(targetDate = new Date().toLocaleDateString()) {
      const totalCount = Object.values(this.stats.hourlyData).reduce(
        (a, b) => a + b,
        0
      );

      if (totalCount > 0) {
        // 标准化日期格式为 YYYY-MM-DD
        let date;
        if (targetDate.includes("/")) {
          // 将 MM/DD/YYYY 或 M/D/YYYY 格式转换为标准格式
          const parts = targetDate.split("/");
          const year = parts[2];
          const month = parts[0].padStart(2, "0");
          const day = parts[1].padStart(2, "0");
          date = `${year}-${month}-${day}`;
        } else if (targetDate.includes("-")) {
          // 检查是否已经是标准格式，如果不是则进行转换
          const parts = targetDate.split("-");
          if (parts.length === 3) {
            const year = parts[0];
            const month = parts[1].padStart(2, "0");
            const day = parts[2].padStart(2, "0");
            date = `${year}-${month}-${day}`;
          } else {
            // 如果格式异常，使用当前日期
            date = new Date().toISOString().split("T")[0];
          }
        } else {
          // 如果是其他格式，使用当前日期
          date = new Date().toISOString().split("T")[0];
        }

        if (!this.stats.dailyData[date]) {
          this.stats.dailyData[date] = {
            total: 0,
            resetCount: 0,
          };
        }

        this.stats.dailyData[date].total += totalCount;
        this.stats.dailyData[date].resetCount++;
      }

      // 清空当前统计
      this.stats.hourlyData = {};
      this.stats.processedBatches = [];
      this.saveStats();

      return totalCount;
    }

    // 加载统计数据
    loadStats() {
      const defaultStats = {
        hourlyData: {},
        dailyData: {},
        processedBatches: [],
        lastUpdated: new Date().toISOString(),
        settings: {
          autoReset: false,
          resetHour: 8,
        },
      };

      try {
        return GM_getValue("obmpReviewStats", defaultStats);
      } catch (error) {
        console.error("加载统计数据失败:", error);
        return defaultStats;
      }
    }

    // 保存统计数据
    saveStats() {
      try {
        this.stats.lastUpdated = new Date().toISOString();
        GM_setValue("obmpReviewStats", this.stats);
      } catch (error) {
        console.error("保存统计数据失败:", error);
      }
    }

    // 获取当前小时的统计数据
    getCurrentHourStats() {
      const now = new Date();
      const hour = `${now.getHours()}:00-${now.getHours() + 1}:00`;
      return {
        hour,
        count: this.stats.hourlyData[hour] || 0,
      };
    }

    // 记录新的数据批次
    recordBatch(batchId, count) {
      if (this.isProcessing) return false;

      try {
        this.isProcessing = true;

        // 在记录新数据前检查日期变更
        this.checkDateChange();

        // 简化检查逻辑
        const processedBatchIds = this.stats.processedBatches;
        const isAlreadyProcessed = processedBatchIds.includes(batchId);

        if (isAlreadyProcessed) {
          console.log("该批次已统计，跳过");
          return false;
        }

        // 使用简单的日期作为小时统计键
        const now = new Date();
        const hour = `${now.getHours()}:00`;

        // 更新小时统计
        if (!this.stats.hourlyData[hour]) {
          this.stats.hourlyData[hour] = 0;
        }
        this.stats.hourlyData[hour] += count;

        // 记录批次ID
        processedBatchIds.push(batchId);

        // 限制数组大小，防止内存泄漏
        if (processedBatchIds.length > 100) {
          this.stats.processedBatches = processedBatchIds.slice(-100);
        }

        this.saveStats();
        return true;
      } finally {
        this.isProcessing = false;
      }
    }

    // 清理过期数据
    cleanup() {
      const thirtyDaysAgo = new Date();
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

      Object.keys(this.stats.dailyData).forEach((date) => {
        if (new Date(date) < thirtyDaysAgo) {
          delete this.stats.dailyData[date];
        }
      });

      this.saveStats();
    }
  }

  // UI管理类
  class StatsUI {
    constructor(statsManager) {
      this.statsManager = statsManager;
      this.panel = null;
      this.isCreating = false;
    }

    // 创建统计面板
    createPanel() {
      if (this.isCreating || this.panel) return;

      this.isCreating = true;

      try {
        const panel = document.createElement("div");
        panel.id = "statsPanel";
        panel.className = "stats-panel";

        panel.innerHTML = `
                  <div class="stats-header">
                      <h3>OBMP审核统计</h3>
                      <div class="stats-buttons">
                          <button id="refresh-stats" class="stats-button">刷新</button>
                          <div class="reset-tooltip">
                              <button id="reset-stats" class="stats-button danger">重置</button>
                              <span class="tooltip-text">手动重置将当前数据归档到今日历史。系统会自动检测日期变更并归档前一天数据。</span>
                          </div>
                          <button id="close-stats" class="stats-button">关闭</button>
                      </div>
                  </div>
                  <div id="stats-content"></div>
                  <div class="auto-archive-info">
                      <strong>自动归档：</strong>系统会在日期变更时自动将前一天的数据归档至对应日期。
                  </div>
                  <div class="last-updated"></div>
              `;

        this.setupEventListeners(panel);
        document.body.appendChild(panel);
        this.panel = panel;
        this.updateContent();
      } finally {
        this.isCreating = false;
      }
    }

    // 设置事件监听
    setupEventListeners(panel) {
      const refreshBtn = panel.querySelector("#refresh-stats");
      const resetBtn = panel.querySelector("#reset-stats");
      const closeBtn = panel.querySelector("#close-stats");

      refreshBtn.addEventListener("click", () => {
        if (refreshBtn.disabled) return;
        refreshBtn.disabled = true;
        this.updateContent();
        setTimeout(() => (refreshBtn.disabled = false), 500);
      });

      resetBtn.addEventListener("click", () => {
        if (
          confirm(
            '确定要手动重置当前统计数据吗？\n\n此操作会将当前统计归档到"今日"历史数据中。\n\n注意：系统会在日期变更时自动归档数据至对应日期。'
          )
        ) {
          const totalCount = this.statsManager.reset();
          this.updateContent();
          GM_notification({
            title: "统计已手动重置",
            text: `已将${totalCount}条数据归档到今日历史`,
            timeout: 2000,
          });
        }
      });

      closeBtn.addEventListener("click", () => this.closePanel());
    }

    // 更新统计内容
    updateContent() {
      if (!this.panel) return;

      const content = this.panel.querySelector("#stats-content");
      if (!content) return;

      const stats = this.statsManager.stats;

      // 今日统计HTML生成
      let hourlyHTML = '<div class="stats-section"><h4>今日统计</h4>';
      const hourData = stats.hourlyData;
      const hours = Object.keys(hourData);

      if (hours.length === 0) {
        hourlyHTML += '<p class="empty-data">暂无数据</p>';
      } else {
        let totalCount = 0;
        // 添加滚动容器
        hourlyHTML +=
          '<div class="stats-today-container"><ul class="stats-list">';

        // 按照小时顺序显示，确保时间顺序正确
        hours
          .sort((a, b) => {
            // 提取小时数进行比较
            const hourA = parseInt(a.split(":")[0]);
            const hourB = parseInt(b.split(":")[0]);
            return hourB - hourA; // 改为降序
          })
          .forEach((hour) => {
            const count = hourData[hour];
            totalCount += count;
            hourlyHTML += `
                      <li>
                          <span class="stats-time">${hour}</span>
                          <span class="stats-count">${count}条</span>
                      </li>`;
          });

        hourlyHTML += `
                  <li class="stats-total">
                      <span>今日总计</span>
                      <span>${totalCount}条</span>
                  </li>
              </ul></div>`;
      }
      hourlyHTML += "</div>";

      // 简化历史数据HTML生成
      let historyHTML = '<div class="stats-section"><h4>历史数据</h4>';
      const dailyData = Object.entries(stats.dailyData || {});

      if (dailyData.length === 0) {
        historyHTML += '<p class="empty-data">暂无历史数据</p>';
      } else {
        // 添加滚动区域容器
        historyHTML += '<div class="stats-history-container">';
        historyHTML += '<ul class="stats-list">';

        // 获取今天的标准日期格式 YYYY-MM-DD
        const today = new Date();
        const todayFormatted = `${today.getFullYear()}-${String(
          today.getMonth() + 1
        ).padStart(2, "0")}-${String(today.getDate()).padStart(2, "0")}`;

        // 显示所有历史数据，不限制天数
        dailyData
          .sort(([a], [b]) => b.localeCompare(a))
          .forEach(([date, data]) => {
            const isToday = date === todayFormatted;
            const dateLabel = isToday ? `${date} (今日)` : date;
            const resetBadge =
              data.resetCount > 1
                ? `<span class="reset-badge">${data.resetCount}次</span>`
                : "";

            historyHTML += `
                          <li${isToday ? ' class="today-history"' : ""}>
                              <span class="stats-time">${dateLabel}${resetBadge}</span>
                              <span class="stats-count">${data.total}条</span>
                          </li>`;
          });

        historyHTML += "</ul></div>";
      }
      historyHTML += "</div>";

      // 一次性更新DOM，减少重绘
      content.innerHTML = hourlyHTML + historyHTML;

      // 使用标准化的日期时间格式
      const lastUpdated = new Date(stats.lastUpdated);
      const timeStr = `${lastUpdated.getHours()}:${String(
        lastUpdated.getMinutes()
      ).padStart(2, "0")}`;

      // 使用标准格式化的日期
      const year = lastUpdated.getFullYear();
      const month = String(lastUpdated.getMonth() + 1).padStart(2, "0");
      const day = String(lastUpdated.getDate()).padStart(2, "0");
      const dateStr = `${year}-${month}-${day}`;

      this.panel.querySelector(".last-updated").innerHTML = `
              最后更新: ${dateStr} ${timeStr}
              <div class="developer-info">Developer: Yangquancheng © 2025</div>
          `;
    }

    // 关闭面板
    closePanel() {
      if (this.panel) {
        this.panel.remove();
        this.panel = null;
      }
    }
  }

  // 数据处理类
  class DataProcessor {
    constructor(statsManager) {
      this.statsManager = statsManager;
      this.processingTimeout = null;
      this.lastProcessedData = null;
      this.processingLock = false;
      this.pendingData = null;

      // 使用防抖处理批量操作
      this.debouncedPrepare = debounce(this._prepareData.bind(this), 300);
      this.debouncedProcess = debounce(this._processSubmission.bind(this), 500);
    }

    // 外部接口使用防抖
    prepareData() {
      this.debouncedPrepare();
    }

    processSubmission() {
      this.debouncedProcess();
    }

    // 实际处理逻辑
    _prepareData() {
      if (this.processingLock) return;

      this.processingLock = true;
      clearTimeout(this.processingTimeout);

      this.processingTimeout = setTimeout(() => {
        try {
          let actualCount = 0;
          let rowIdentifiers = [];

          // 从DOM获取表格数据
          console.log("从DOM获取表格数据");

          const rows = this._getTableRows();
          console.log(`找到总行数: ${rows.length}`);

          if (!rows || rows.length === 0) {
            console.log("找不到有效的表格行");
            this.statsManager.showNotification(
              "❌ 无法获取数据，请先点击获取按钮加载数据",
              "error"
            );
            this.processingLock = false;
            return;
          }

          // 只记录有效的、可见的行
          const validRows = Array.from(rows).filter((row) =>
            this._isValidRow(row)
          );
          actualCount = validRows.length;
          console.log(`有效行数: ${actualCount}`);

          // 生成数据标识符
          rowIdentifiers = validRows
            .map((row) => {
              const cells = row.querySelectorAll(".cell");
              const idCell = cells[0];
              return idCell ? idCell.textContent.trim() : "";
            })
            .filter((id) => id);

          console.log(`从DOM获取到${actualCount}行有效数据`);

          if (actualCount === 0) {
            this.statsManager.showNotification(
              "⚠️ 未找到有效数据，请确保表格已加载",
              "error"
            );
            this.processingLock = false;
            return;
          }

          // 显示DOM获取成功提示
          this.statsManager.showNotification(
            `✅ 数据已准备完成，共 ${actualCount} 条记录`,
            "success"
          );

          // 存储待处理数据
          this.pendingData = {
            count: actualCount,
            identifiers: rowIdentifiers,
            timestamp: new Date().getTime(),
          };

          console.log("数据已准备，等待提交", this.pendingData);
        } catch (error) {
          console.error("准备数据时出错:", error);
        } finally {
          this.processingLock = false;
        }
      }, 300);
    }

    // 增强的选择器策略
    _getTableRows() {
      const selectors = [
        "tbody tr.el-table__row:not(.is-placeholder)",
        ".el-table__body tbody tr.el-table__row:not(.is-placeholder)",
        ".el-table__body-wrapper tbody tr.el-table__row:not(.is-placeholder)",
        ".el-table tbody tr:not(.is-placeholder)",
      ];

      for (const selector of selectors) {
        try {
          const rows = document.querySelectorAll(selector);
          if (rows.length > 0) {
            console.log(`使用选择器: ${selector}, 找到 ${rows.length} 行`);
            return rows;
          }
        } catch (error) {
          console.warn(`选择器 ${selector} 执行失败:`, error);
        }
      }

      console.warn("所有选择器都未找到有效行");
      return [];
    }

    // 增强的行有效性验证
    _isValidRow(row) {
      try {
        // 基础可见性检查
        if (!row.offsetParent && row.style.display !== "table-row") {
          return false;
        }

        // 检查是否被隐藏
        if (
          row.classList.contains("hidden") ||
          row.style.display === "none" ||
          row.style.visibility === "hidden"
        ) {
          return false;
        }

        // 检查是否有内容
        const cellContent = row.querySelector(".cell");
        if (!cellContent) {
          return false;
        }

        // 检查是否为占位符或加载行
        if (
          row.classList.contains("is-placeholder") ||
          row.classList.contains("loading-row") ||
          row.querySelector(".el-table__empty-text")
        ) {
          return false;
        }

        return true;
      } catch (error) {
        console.warn("行验证失败:", error);
        return false;
      }
    }

    _processSubmission() {
      // 如果没有待处理数据，尝试获取表格数据
      if (!this.pendingData) {
        console.log("尝试获取表格数据...");

        try {
          const rows = this._getTableRows();

          if (rows && rows.length > 0) {
            const validRows = Array.from(rows).filter((row) =>
              this._isValidRow(row)
            );
            const actualCount = validRows.length;

            if (actualCount > 0) {
              console.log(`发现${actualCount}行有效数据，直接统计`);
              this.pendingData = {
                count: actualCount,
                timestamp: new Date().getTime(),
              };
            }
          }
        } catch (error) {
          console.error("获取数据时出错:", error);
        }

        // 如果仍然没有数据，显示提示并退出
        if (!this.pendingData) {
          console.log("无法获取有效数据，请确保表格已加载数据");
          this.statsManager.showNotification(
            "❌ 无法获取数据，请重新尝试",
            "error"
          );
          return;
        }
      }

      // 处理数据统计
      try {
        const { count, timestamp } = this.pendingData;
        const batchId = `${timestamp}-${count}`;

        if (this.statsManager.recordBatch(batchId, count)) {
          this.statsManager.showNotification(
            `✅ 已统计: ${count}条`,
            "success"
          );
        }

        this.pendingData = null;
      } catch (error) {
        console.error("处理提交数据时出错:", error);
      }
    }
  }

  // 主程序
  function main() {
    // 添加样式
    GM_addStyle(`
          .stats-panel {
              position: fixed;
              right: 20px;
              top: 20px;
              width: 460px;
              background: linear-gradient(to bottom right, #ffffff, #f8f9fa);
              border-radius: 12px;
              box-shadow: 0 4px 24px rgba(0, 0, 0, 0.08);
              padding: 24px;
              z-index: 9999;
              font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
              border: 1px solid rgba(0, 0, 0, 0.05);
          }

          .stats-header {
              display: flex;
              justify-content: space-between;
              align-items: center;
              margin-bottom: 24px;
              padding-bottom: 16px;
              border-bottom: 2px solid #f0f2f5;
          }

          .stats-header h3 {
              margin: 0;
              font-size: 20px;
              font-weight: 600;
              color: #1f2937;
          }

          .stats-buttons {
              display: flex;
              gap: 8px;
          }

          .stats-button {
              padding: 8px 16px;
              border-radius: 6px;
              border: 1px solid #e5e7eb;
              background: white;
              color: #374151;
              cursor: pointer;
              font-size: 14px;
              font-weight: 500;
              transition: all 0.2s ease;
              display: flex;
              align-items: center;
              justify-content: center;
              min-width: 80px;
          }

          .stats-button:hover {
              background: #f9fafb;
              border-color: #d1d5db;
          }

          .stats-button:active {
              transform: translateY(1px);
          }

          .stats-button.danger {
              color: #dc2626;
              border-color: #fecaca;
          }

          .stats-button.danger:hover {
              background: #fef2f2;
              border-color: #fca5a5;
          }

          .stats-button:disabled {
              opacity: 0.6;
              cursor: not-allowed;
          }

          .stats-section {
              background: white;
              border-radius: 10px;
              padding: 20px;
              margin-bottom: 20px;
              border: 1px solid #f0f2f5;
          }

          .stats-section h4 {
              margin: 0 0 16px 0;
              font-size: 16px;
              font-weight: 600;
              color: #374151;
              display: flex;
              align-items: center;
              gap: 8px;
          }

          .stats-section h4::before {
              content: '';
              display: inline-block;
              width: 4px;
              height: 16px;
              background: #3b82f6;
              border-radius: 2px;
          }

          .stats-list {
              list-style: none;
              padding: 0;
              margin: 0;
          }

          .stats-list li {
              display: flex;
              justify-content: space-between;
              align-items: center;
              padding: 12px 16px;
              margin-bottom: 8px;
              background: #f8fafc;
              border-radius: 8px;
              transition: all 0.2s ease;
          }

          .stats-list li:hover {
              background: #f1f5f9;
              transform: translateX(4px);
          }

          .stats-list li:last-child {
              margin-bottom: 0;
          }

          .stats-list .stats-total {
              background: #ebf5ff;
              border: 1px dashed #93c5fd;
              font-weight: 600;
              color: #1e40af;
          }

          .stats-list .stats-total:hover {
              background: #dbeafe;
              transform: none;
          }

          .empty-data {
              text-align: center;
              color: #6b7280;
              padding: 32px 0;
              font-size: 14px;
              background: #f9fafb;
              border-radius: 8px;
              border: 1px dashed #e5e7eb;
          }

          .last-updated {
              font-size: 13px;
              color: #6b7280;
              text-align: right;
              margin-top: 16px;
              padding-top: 16px;
              border-top: 1px solid #f0f2f5;
          }

          #stats-btn {
              position: fixed;
              left: 20px;
              bottom: 20px;
              z-index: 9999;
              padding: 10px 20px;
              border-radius: 8px;
              background: linear-gradient(135deg, #3b82f6, #2563eb);
              color: white;
              border: none;
              cursor: pointer;
              font-size: 14px;
              font-weight: 500;
              box-shadow: 0 2px 8px rgba(59, 130, 246, 0.25);
              transition: all 0.3s ease;
          }

          #stats-btn:hover {
              transform: translateY(-2px);
              box-shadow: 0 4px 12px rgba(59, 130, 246, 0.35);
              background: linear-gradient(135deg, #2563eb, #1d4ed8);
          }

          #stats-btn:active {
              transform: translateY(0);
          }

          /* 添加数字显示样式 */
          .stats-count {
              font-family: 'Roboto Mono', monospace;
              font-size: 15px;
              font-weight: 500;
              color: #1e40af;
              background: rgba(59, 130, 246, 0.1);
              padding: 2px 8px;
              border-radius: 4px;
          }

          /* 添加时间显示样式 */
          .stats-time {
              color: #4b5563;
              font-weight: 500;
          }

          /* 添加重置次数标记样式 */
          .reset-badge {
              font-size: 12px;
              padding: 2px 6px;
              background: #fee2e2;
              color: #991b1b;
              border-radius: 4px;
              margin-left: 8px;
          }

          /* 添加批量提交按钮活跃样式 */
          button.el-button--primary[data-submit-listener-added="true"] {
              position: relative;
          }
          
          button.el-button--primary[data-submit-listener-added="true"]::after {
              content: '';
              position: absolute;
              right: -5px;
              top: -5px;
              width: 8px;
              height: 8px;
              background: #10b981;
              border-radius: 50%;
              box-shadow: 0 0 0 2px white;
          }

          /* 今日历史记录样式 */
          .today-history {
              background: #f0f9ff !important;
              border-left: 3px solid #3b82f6;
          }

          /* 添加自动归档提示 */
          .auto-archive-info {
              font-size: 12px;
              color: #6b7280;
              margin-top: 12px;
              padding: 8px 12px;
              background: #f3f4f6;
              border-radius: 6px;
              line-height: 1.4;
          }

          /* 更新重置按钮提示 */
          .reset-tooltip {
              position: relative;
              display: inline-block;
          }

          .reset-tooltip .tooltip-text {
              visibility: hidden;
              width: 200px;
              background-color: #374151;
              color: #fff;
              text-align: center;
              border-radius: 6px;
              padding: 8px;
              position: absolute;
              z-index: 1;
              bottom: 125%;
              left: 50%;
              transform: translateX(-50%);
              opacity: 0;
              transition: opacity 0.3s;
              font-size: 12px;
              font-weight: normal;
              line-height: 1.4;
          }

          .reset-tooltip:hover .tooltip-text {
              visibility: visible;
              opacity: 1;
          }

          /* 历史数据滚动容器 */
          .stats-history-container {
              max-height: 250px;
              overflow-y: auto;
              margin: 0 -5px;
              padding: 0 5px;
              scrollbar-width: thin;
              scrollbar-color: #d1d5db #f3f4f6;
          }

          .stats-history-container::-webkit-scrollbar {
              width: 6px;
          }

          .stats-history-container::-webkit-scrollbar-track {
              background: #f3f4f6;
              border-radius: 4px;
          }

          .stats-history-container::-webkit-scrollbar-thumb {
              background-color: #d1d5db;
              border-radius: 4px;
          }

          .stats-history-container .stats-list {
              padding-right: 5px;
          }

          .developer-info {
              font-size: 12px;
              color: #9ca3af;
              margin-top: 8px;
              text-align: right;
              font-style: normal;
          }

          /* 今日统计滚动容器 */
          .stats-today-container {
              max-height: 200px;
              overflow-y: auto;
              margin: 0 -5px;
              padding: 0 5px;
              scrollbar-width: thin;
              scrollbar-color: #d1d5db #f3f4f6;
          }

          .stats-today-container::-webkit-scrollbar {
              width: 6px;
          }

          .stats-today-container::-webkit-scrollbar-track {
              background: #f3f4f6;
              border-radius: 4px;
          }

          .stats-today-container::-webkit-scrollbar-thumb {
              background-color: #d1d5db;
              border-radius: 4px;
          }

          .stats-today-container .stats-list {
              padding-right: 5px;
          }
      `);

    // 初始化
    const statsManager = new StatsManager();

    // 不需要在这里调用checkDateChange，因为我们在构造函数中已经调用了checkDateChangeOnStartup

    // 显示初始自动归档通知（如果已进行自动归档）
    setTimeout(() => {
      // 使用标准化的日期格式处理
      const today = new Date();
      const currentDateObj = new Date();
      const currentYear = currentDateObj.getFullYear();
      const currentMonth = String(currentDateObj.getMonth() + 1).padStart(
        2,
        "0"
      );
      const currentDay = String(currentDateObj.getDate()).padStart(2, "0");
      const currentDate = `${currentYear}-${currentMonth}-${currentDay}`;

      const lastUpdatedObj = new Date(statsManager.stats.lastUpdated);
      const lastYear = lastUpdatedObj.getFullYear();
      const lastMonth = String(lastUpdatedObj.getMonth() + 1).padStart(2, "0");
      const lastDay = String(lastUpdatedObj.getDate()).padStart(2, "0");
      const lastUpdateDate = `${lastYear}-${lastMonth}-${lastDay}`;

      // 仅当今天首次加载且日期变更时显示欢迎通知
      if (
        currentDate !== lastUpdateDate &&
        !sessionStorage.getItem("welcomeShown")
      ) {
        sessionStorage.setItem("welcomeShown", "true");
        GM_notification({
          title: "OBMP审核统计已启动",
          text: "支持自动日期归档，无需手动重置",
          timeout: 3000,
        });
      }
    }, 1000);

    const statsUI = new StatsUI(statsManager);
    const dataProcessor = new DataProcessor(statsManager);

    // 添加统计按钮
    const statsBtn = document.createElement("button");
    statsBtn.id = "stats-btn";
    statsBtn.title = "点击查看文本审核统计";
    statsBtn.innerHTML = `
        <span class="stats-icon">📊</span>
        <span class="stats-text">显示统计</span>
    `;
    statsBtn.style.cssText = `
          position: fixed;
          left: -110px;
          bottom: 20px;
          z-index: 9999;
          padding: 10px 15px;
          border-radius: 0 8px 8px 0;
          background: linear-gradient(135deg, #3b82f6, #2563eb);
          color: white;
          border: none;
          cursor: pointer;
          font-size: 14px;
          font-weight: 500;
          box-shadow: 0 2px 8px rgba(59, 130, 246, 0.25);
          transition: all 0.3s ease;
          display: flex;
          align-items: center;
          gap: 8px;
          min-width: 120px;
          overflow: hidden;
      `;

    // 鼠标悬停事件
    statsBtn.addEventListener("mouseenter", () => {
      statsBtn.style.left = "0px";
      const icon = statsBtn.querySelector(".stats-icon");
      const text = statsBtn.querySelector(".stats-text");
      if (icon) icon.style.transform = "rotate(360deg)";
      if (text) {
        text.style.opacity = "1";
        text.style.transform = "translateX(0)";
      }
    });

    statsBtn.addEventListener("mouseleave", () => {
      statsBtn.style.left = "-110px";
      const icon = statsBtn.querySelector(".stats-icon");
      const text = statsBtn.querySelector(".stats-text");
      if (icon) icon.style.transform = "rotate(0deg)";
      if (text) {
        text.style.opacity = "0.7";
        text.style.transform = "translateX(-10px)";
      }
    });

    // 添加内部元素样式
    const style = document.createElement("style");
    style.textContent = `
        #stats-btn .stats-icon {
            transition: transform 0.3s ease;
            font-size: 16px;
        }
        #stats-btn .stats-text {
            transition: all 0.3s ease;
            opacity: 0.7;
            transform: translateX(-10px);
        }
    `;
    document.head.appendChild(style);

    // 初始显示提示
    setTimeout(() => {
      statsBtn.style.left = "0px";
      const icon = statsBtn.querySelector(".stats-icon");
      const text = statsBtn.querySelector(".stats-text");
      if (icon) icon.style.transform = "rotate(360deg)";
      if (text) {
        text.style.opacity = "1";
        text.style.transform = "translateX(0)";
      }

      setTimeout(() => {
        statsBtn.style.left = "-110px";
        if (icon) icon.style.transform = "rotate(0deg)";
        if (text) {
          text.style.opacity = "0.7";
          text.style.transform = "translateX(-10px)";
        }
      }, 2000);
    }, 1000);

    statsBtn.addEventListener("click", () => statsUI.createPanel());
    document.body.appendChild(statsBtn);

    // 简化按钮绑定逻辑，减少DOM查询频率
    let getButtonElement = null;
    let submitButtonElements = [];

    // 更高效的获取按钮绑定
    function bindGetDataButton() {
      if (getButtonElement && getButtonElement.dataset.getListenerAdded) {
        return true;
      }

      const getDataBtn = document.querySelector(
        ".el-form-item__content button.el-button--primary"
      );
      if (!getDataBtn) return false;

      if (!getDataBtn.dataset.getListenerAdded) {
        getDataBtn.dataset.getListenerAdded = "true";
        getDataBtn.addEventListener("click", () => {
          console.log("获取按钮被点击");
          setTimeout(() => dataProcessor.prepareData(), 500);
        });
        getButtonElement = getDataBtn;
        return true;
      }

      return false;
    }

    // 更高效的提交按钮绑定
    function bindSubmitButtons() {
      // 避免重复查询已绑定的按钮
      const boundIds = submitButtonElements.map(
        (el) => el.dataset.uniqueId || ""
      );

      // 针对性选择批量提交按钮，减少查询范围
      const potentialBtns = [
        ...document.querySelectorAll(
          'button.el-button--primary[style*="float: right"]'
        ),
        ...document.querySelectorAll(
          'button.el-button--primary[accesskey="s"]'
        ),
      ];

      let newButtonFound = false;

      potentialBtns.forEach((btn, index) => {
        // 生成唯一ID
        if (!btn.dataset.uniqueId) {
          btn.dataset.uniqueId = `submit-btn-${index}-${Date.now()}`;
        }

        // 跳过已绑定的按钮
        if (boundIds.includes(btn.dataset.uniqueId)) return;

        // 检查是否是批量提交按钮
        const spanEl = btn.querySelector("span");
        if (
          spanEl &&
          spanEl.textContent.includes("批量提交") &&
          !btn.dataset.submitListenerAdded
        ) {
          btn.dataset.submitListenerAdded = "true";

          // 简化点击处理
          btn.addEventListener("click", () => {
            dataProcessor.processSubmission();
          });

          // 标记按钮已绑定
          submitButtonElements.push(btn);
          newButtonFound = true;
        }
      });

      return newButtonFound;
    }

    // 简化的按钮检查逻辑
    function setupButtonListeners() {
      // 初始绑定
      bindGetDataButton();
      bindSubmitButtons();

      // 减少MutationObserver的工作量，只观察按钮可能出现的区域
      const buttonAreaObserver = new MutationObserver(() => {
        const getFound = bindGetDataButton();
        const submitFound = bindSubmitButtons();

        // 如果找到了新按钮，记录日志但不断开观察器
        // 因为按钮可能会动态变化
        if (getFound || submitFound) {
          console.log("发现并绑定了新按钮");
        }
      });

      // 只观察限定区域，减少性能开销
      const controlAreas = document.querySelectorAll(
        ".el-form, .el-button--primary"
      );
      controlAreas.forEach((area) => {
        if (area) {
          buttonAreaObserver.observe(area.parentNode || document.body, {
            childList: true,
            subtree: true,
          });
        }
      });

      // 每10秒才检查一次，减少频率
      setInterval(() => {
        bindGetDataButton();
        bindSubmitButtons();
      }, 10000);
    }

    // 启动按钮监听
    setupButtonListeners();

    // 定期清理过期数据
    setInterval(() => statsManager.cleanup(), 24 * 60 * 60 * 1000);
  }

  // 启动脚本
  if (document.readyState === "complete") {
    main();
  } else {
    window.addEventListener("load", main);
  }
})();

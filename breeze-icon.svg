<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 64 64" width="64" height="64">
  <!-- 背景圆形 -->
  <circle cx="32" cy="32" r="30" fill="#2c5aa0" stroke="#1e3d6f" stroke-width="2"/>
  
  <!-- 鼠标主体 -->
  <g transform="translate(32, 32)">
    <!-- 鼠标外壳 -->
    <ellipse cx="0" cy="0" rx="8" ry="12" fill="#f5f5f5" stroke="#cccccc" stroke-width="1.5"/>
    
    <!-- 鼠标左键 -->
    <path d="M-8 -8 Q-8 -12 0 -12 L0 0 L-8 0 Z" fill="#e8e8e8" stroke="#cccccc" stroke-width="1"/>
    
    <!-- 鼠标右键 -->
    <path d="M0 -12 Q8 -12 8 -8 L8 0 L0 0 Z" fill="#e8e8e8" stroke="#cccccc" stroke-width="1"/>
    
    <!-- 滚轮 -->
    <rect x="-1" y="-8" width="2" height="4" rx="1" fill="#999999"/>
    
    <!-- 点击动画效果 -->
    <circle cx="0" cy="0" r="3" fill="none" stroke="#ff6b6b" stroke-width="2" opacity="0.8">
      <animate attributeName="r" values="3;8;3" dur="1.5s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0.8;0.2;0.8" dur="1.5s" repeatCount="indefinite"/>
    </circle>
    
    <!-- 自动化指示箭头 -->
    <g transform="translate(12, -8)">
      <path d="M0 0 L6 3 L0 6 Z" fill="#32cd32">
        <animateTransform attributeName="transform" type="translate" values="0,0;3,0;0,0" dur="2s" repeatCount="indefinite"/>
      </path>
    </g>
    
    <!-- 自动化轨迹线 -->
    <path d="M10 -5 Q15 0 10 5" stroke="#32cd32" stroke-width="2" fill="none" opacity="0.7">
      <animate attributeName="stroke-dasharray" values="0,15;8,7;15,0;8,7;0,15" dur="2s" repeatCount="indefinite"/>
    </path>
  </g>
</svg>
// ==UserScript==
// @name         快照智能工单计时器
// @namespace    http://tampermonkey.net/
// @version      1.6
// @description  点击获取按钮后自动计时，可自定义计时时间
// <AUTHOR>
// @match        https://inspect.cc.163.com/#/live_snapshot/inspect
// @updateURL    https://gengxin.geluman.cn/OBMP/Automatictiming.user.js
// @downloadURL  https://gengxin.geluman.cn/OBMP/Automatictiming.user.js
// @grant        GM_addStyle
// @grant        GM_getValue
// @grant        GM_setValue
// @grant        GM_registerMenuCommand
// ==/UserScript==

(function () {
  "use strict";

  // 配置参数
  const CONFIG = {
    checkDelay: 800, // 检测延迟(毫秒)
  };

  // 获取用户设置的倒计时时间
  function getDelayTime() {
    return GM_getValue("delayTime", 40); // 默认40秒
  }

  // 保存用户设置的倒计时时间
  function setDelayTime(time) {
    GM_setValue("delayTime", time);
  }

  // 获取用户设置的提醒方式
  function getReminderType() {
    return GM_getValue("reminderType", "voice"); // 默认语音提醒
  }

  // 保存用户设置的提醒方式
  function setReminderType(type) {
    GM_setValue("reminderType", type);
  }

  // 音频提醒函数

  let audioCtx;
  let audioBuffer;
  // 预加载音频（首次点击时调用）
  async function preloadAudio() {
    if (!audioCtx) {
      audioCtx = new (window.AudioContext || window.webkitAudioContext)();
      try {
        const response = await fetch(
          "https://gengxin.geluman.cn/audio/submit%20ticket.mp3"
        );
        const arrayBuffer = await response.arrayBuffer();
        audioBuffer = await audioCtx.decodeAudioData(arrayBuffer);
      } catch (e) {
        console.error("[计时器] 音频预加载失败:", e);
      }
    }
  }
  function playVoiceReminder() {
    if (getReminderType() !== "voice") return;
    if (!audioCtx || !audioBuffer)
      return preloadAudio().then(playVoiceReminder);
    if (audioCtx.state === "suspended") audioCtx.resume();
    const source = audioCtx.createBufferSource();
    source.buffer = audioBuffer;
    source.connect(audioCtx.destination);
    source.start();
  }

  // 状态变量
  let isCounting = false;
  let countdownInterval;
  let cardObserver;
  let ignoreCardDetection = false; // 忽略卡片检测的标志
  let countdownStartTime = 0; // 倒计时开始时间戳
  let countdownDuration = 0; // 倒计时总持续时间(毫秒)

  // 创建计时显示器
  const timerDisplay = document.createElement("div");
  Object.assign(timerDisplay.style, {
    position: "fixed",
    top: "35px",
    right: "55px",
    background: "rgba(0,0,0,0.7)",
    color: "white",
    padding: "5px 10px",
    borderRadius: "3px",
    fontFamily: "Arial",
    fontSize: "12px",
    zIndex: "9999",
    minWidth: "80px",
    textAlign: "center",
  });
  timerDisplay.textContent = "就绪";
  document.body.appendChild(timerDisplay);

  // 创建设置面板
  function createSettingsPanel() {
    // 移除已存在的设置面板
    const existingPanel = document.getElementById("timing-settings-panel");
    if (existingPanel) {
      existingPanel.remove();
    }

    // 创建遮罩层
    const overlay = document.createElement("div");
    overlay.id = "timing-settings-overlay";
    Object.assign(overlay.style, {
      position: "fixed",
      top: "0",
      left: "0",
      width: "100%",
      height: "100%",
      background: "rgba(0, 0, 0, 0.5)",
      zIndex: "9999",
      opacity: "0",
      transition: "opacity 0.3s ease",
    });

    // 创建设置面板
    const panel = document.createElement("div");
    panel.id = "timing-settings-panel";
    Object.assign(panel.style, {
      position: "fixed",
      top: "50%",
      left: "50%",
      transform: "translate(-50%, -50%) scale(0.95)",
      background: "#ffffff",
      borderRadius: "8px",
      padding: "0",
      boxShadow:
        "0 4px 20px rgba(0, 0, 0, 0.15), 0 0 0 1px rgba(0, 0, 0, 0.05)",
      zIndex: "10000",
      fontFamily:
        "-apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif",
      width: "360px",
      opacity: "0",
      transition: "all 0.3s ease",
      overflow: "hidden",
    });

    const currentTime = getDelayTime();
    const currentReminder = getReminderType();

    panel.innerHTML = `
      <!-- 头部区域 -->
      <div style="
        background: #ffffff;
        padding: 20px 24px 16px;
        border-bottom: 1px solid #f0f0f0;
      ">
        <div style="display: flex; align-items: center;">
          <div style="
            width: 36px; height: 36px;
            background: #1890ff;
            border-radius: 6px;
            display: flex; align-items: center; justify-content: center;
            margin-right: 12px;
          ">
            <span style="font-size: 18px; color: white;">⏱️</span>
          </div>
          <div>
            <h3 style="
              margin: 0;
              color: #262626;
              font-size: 16px;
              font-weight: 600;
            ">计时器设置</h3>
            <p style="
              margin: 2px 0 0 0;
              color: #8c8c8c;
              font-size: 12px;
            ">自定义倒计时时间和提醒方式</p>
          </div>
        </div>
      </div>

      <!-- 内容区域 -->
      <div style="padding: 24px;">
        <!-- 倒计时时间设置 -->
        <div style="margin-bottom: 20px;">
          <label style="
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: #262626;
            font-size: 14px;
          ">
            ⏰ 倒计时时间 (秒)
          </label>
          <input type="number" id="delay-time-input"
                 value="${currentTime}"
                 min="1" max="300"
                 style="
                   width: 100%;
                   padding: 8px 12px;
                   border: 1px solid #d9d9d9;
                   border-radius: 6px;
                   font-size: 14px;
                   color: #262626;
                   transition: all 0.3s ease;
                   outline: none;
                   box-sizing: border-box;
                 "
                 placeholder="输入倒计时秒数">
          <div style="
            margin-top: 6px;
            font-size: 12px;
            color: #8c8c8c;
          ">
            建议范围：10-120秒，当前默认：40秒
          </div>
        </div>

        <!-- 提醒方式设置 -->
        <div style="margin-bottom: 20px;">
          <label style="
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: #262626;
            font-size: 14px;
          ">
            🔔 提醒方式
          </label>
          <div style="display: flex; gap: 12px;">
            <label style="
              display: flex;
              align-items: center;
              cursor: pointer;
              font-size: 14px;
              color: #262626;
            ">
              <input type="radio" name="reminder" value="voice"
                     ${currentReminder === "voice" ? "checked" : ""}
                     style="margin-right: 6px;">
              🔊 音频提醒
            </label>
            <label style="
              display: flex;
              align-items: center;
              cursor: pointer;
              font-size: 14px;
              color: #262626;
            ">
              <input type="radio" name="reminder" value="none"
                     ${currentReminder === "none" ? "checked" : ""}
                     style="margin-right: 6px;">
              🔇 关闭提醒
            </label>
          </div>
          <div style="
            margin-top: 6px;
            font-size: 12px;
            color: #8c8c8c;
          ">
            音频提醒会在倒计时结束时播放"该提交快照咯"
          </div>
        </div>

        <!-- 按钮区域 -->
        <div style="
          display: flex;
          gap: 8px;
          justify-content: flex-end;
          margin-top: 24px;
        ">
          <button id="cancel-settings" style="
            padding: 6px 15px;
            border: 1px solid #d9d9d9;
            background: #ffffff;
            color: #595959;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s ease;
          ">
            取消
          </button>
          <button id="save-settings" style="
            padding: 6px 15px;
            border: none;
            background: #1890ff;
            color: white;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s ease;
          ">
            保存设置
          </button>
        </div>
      </div>
    `;

    // 添加到页面
    document.body.appendChild(overlay);
    document.body.appendChild(panel);

    // 入场动画
    requestAnimationFrame(() => {
      overlay.style.opacity = "1";
      panel.style.opacity = "1";
      panel.style.transform = "translate(-50%, -50%) scale(1)";
    });

    // 绑定事件
    const input = document.getElementById("delay-time-input");
    const saveBtn = document.getElementById("save-settings");
    const cancelBtn = document.getElementById("cancel-settings");

    // 添加输入框交互效果
    input.addEventListener("focus", function () {
      this.style.borderColor = "#40a9ff";
      this.style.boxShadow = "0 0 0 2px rgba(24, 144, 255, 0.2)";
    });

    input.addEventListener("blur", function () {
      this.style.borderColor = "#d9d9d9";
      this.style.boxShadow = "none";
    });

    // 添加按钮悬停效果
    cancelBtn.addEventListener("mouseenter", function () {
      this.style.borderColor = "#40a9ff";
      this.style.color = "#40a9ff";
    });

    cancelBtn.addEventListener("mouseleave", function () {
      this.style.borderColor = "#d9d9d9";
      this.style.color = "#595959";
    });

    saveBtn.addEventListener("mouseenter", function () {
      this.style.background = "#40a9ff";
    });

    saveBtn.addEventListener("mouseleave", function () {
      this.style.background = "#1890ff";
    });

    // 输入验证
    input.addEventListener("input", function () {
      const value = parseInt(this.value);
      if (value < 1) this.value = 1;
      if (value > 300) this.value = 300;
    });

    // 关闭面板函数
    function closePanel() {
      panel.style.opacity = "0";
      panel.style.transform = "translate(-50%, -50%) scale(0.95)";
      overlay.style.opacity = "0";

      setTimeout(() => {
        if (panel.parentNode) panel.remove();
        if (overlay.parentNode) overlay.remove();
      }, 300);
    }

    // 保存设置
    saveBtn.addEventListener("click", function () {
      const newTime = parseInt(input.value);
      const selectedReminder = document.querySelector(
        'input[name="reminder"]:checked'
      ).value;

      if (newTime >= 1 && newTime <= 300) {
        setDelayTime(newTime);
        setReminderType(selectedReminder);

        const reminderText =
          selectedReminder === "voice" ? "语音提醒" : "关闭提醒";
        showTempMessage(`✅ 设置已保存: ${newTime}秒, ${reminderText}`, false);
        closePanel();
      }
    });

    // 取消设置
    cancelBtn.addEventListener("click", closePanel);
    overlay.addEventListener("click", closePanel);

    // 按ESC键关闭
    function handleEscape(e) {
      if (e.key === "Escape") {
        closePanel();
        document.removeEventListener("keydown", handleEscape);
      }
    }
    document.addEventListener("keydown", handleEscape);

    // 聚焦输入框
    setTimeout(() => {
      input.focus();
      input.select();
    }, 100);
  }

  // 支持的按钮配置 - 修改后版本，移除易变属性但保留样式类
  const BUTTONS = [
    {
      selector: ".ant-btn.ant-btn-info",
      text: "获 取",
    },
    {
      selector: ".ant-btn.ant-btn-primary",
      text: "关闭并获取",
    },
    {
      selector: ".ant-btn.ant-btn-info",
      text: "提交并获取",
    },
  ];

  // 检测无数据状态或错误状态
  function checkNoData() {
    const alerts = document.querySelectorAll(".el-message__content");
    return Array.from(alerts).some((alert) => {
      const text = alert.textContent;
      return (
        text.includes("暂无待审核数据") ||
        text.includes("请先签入再开始工作吧") ||
        text.includes("可选配置为空，请联系组长配置派单")
      );
    });
  }

  // 改进的按钮检测函数
  function isTargetButton(element, btnConfig) {
    if (!element || !element.matches(btnConfig.selector)) return false;

    // 精确匹配按钮文本
    return element.textContent.trim() === btnConfig.text;
  }

  // 检查是否滚动到底部
  function isScrollBottom() {
    const scrollBox = document.getElementById("scrollBox") || document.body;
    const e = scrollBox.scrollTop;
    const t = scrollBox.clientHeight;
    const n = scrollBox.scrollHeight;
    return e + t >= n;
  }

  // 主点击监听
  document.addEventListener("click", function (e) {
    // 检查是否点击了历史快照按钮，如果是则直接返回，不处理
    const clickedElement = e.target.closest("button");
    if (clickedElement) {
      const buttonText = clickedElement.textContent?.trim() || "";
      if (buttonText.includes("历史截图")) {
        console.log("[计时器] 检测到历史快照按钮点击，忽略处理");
        // 设置忽略标志，防止DOM变化触发倒计时
        ignoreCardDetection = true;
        setTimeout(() => {
          ignoreCardDetection = false;
        }, 1000); // 1秒后恢复检测
        return;
      }
    }

    // 检查是否点击了"仅提交"按钮
    const submitOnlyElement = e.target.closest(".ant-btn.ant-btn-info");
    if (submitOnlyElement) {
      const submitOnlyText = submitOnlyElement.textContent?.trim() || "";
      if (submitOnlyText === "仅提交") {
        console.log("[计时器] 检测到仅提交按钮点击，停止倒计时");
        // 停止倒计时并显示就绪状态
        resetCountdown();
        timerDisplay.textContent = "就绪";
        return;
      }
    }

    // 检查是否点击了目标按钮
    for (const btn of BUTTONS) {
      const element = e.target.closest(btn.selector);
      if (element && isTargetButton(element, btn)) {
        // 双重检查：确保不是历史快照按钮
        const elementText = element.textContent?.trim() || "";
        if (elementText.includes("历史截图")) {
          console.log("[计时器] 目标按钮检查中发现历史快照按钮，跳过处理");
          continue;
        }

        e.stopImmediatePropagation();

        setTimeout(() => {
          if (checkNoData()) {
            showTempMessage("无待审核数据");
          } else {
            startCountdown(); // 移除isCounting限制，支持重置
          }
        }, CONFIG.checkDelay);
        break;
      }
    }
  });

  // 重置倒计时
  function resetCountdown() {
    if (countdownInterval) {
      clearInterval(countdownInterval);
    }
    isCounting = false;
  }

  // 开始倒计时
  function startCountdown() {
    // 如果正在倒计时，先重置
    if (isCounting) {
      resetCountdown();
      showTempMessage("检测到新工单，重新计时", false);
    }

    isCounting = true;
    // 记录倒计时开始时间和持续时间
    countdownStartTime = Date.now();
    countdownDuration = getDelayTime() * 1000; // 转换为毫秒

    // 初始显示
    updateTimerDisplay(getDelayTime());

    countdownInterval = setInterval(() => {
      // 计算已经过的时间
      const elapsed = Date.now() - countdownStartTime;
      // 计算剩余时间
      const remainingMs = Math.max(0, countdownDuration - elapsed);
      const remainingSeconds = Math.ceil(remainingMs / 1000);

      updateTimerDisplay(remainingSeconds);

      if (remainingMs <= 0) {
        finishCountdown();
      }
    }, 1000);
  }

  // 结束倒计时
  function finishCountdown() {
    clearInterval(countdownInterval);
    isCounting = false;
    showTempMessage("可再次获取", false);
    playVoiceReminder(); // 播放语音提醒
  }

  // 更新计时显示
  function updateTimerDisplay(seconds) {
    timerDisplay.textContent = `倒计时: ${seconds}s`;
    timerDisplay.style.color = seconds <= 5 ? "#ffcc00" : "white";
  }

  // 显示临时消息
  function showTempMessage(msg) {
    timerDisplay.textContent = msg;

    setTimeout(() => {
      if (!isCounting) timerDisplay.textContent = "就绪";
    }, 1500);
  }

  // 设置卡片检测
  function setupCardObserver() {
    // 防抖处理，避免多个卡片同时添加时重复触发
    let cardDetectionTimeout;

    cardObserver = new MutationObserver((mutations) => {
      let hasNewCards = false;

      mutations.forEach((mutation) => {
        if (mutation.type === "childList") {
          mutation.addedNodes.forEach((node) => {
            if (node.nodeType === 1) {
              // 检查添加的节点本身或其子节点是否包含card
              if (
                node.classList?.contains("card") ||
                node.querySelector?.(".card")
              ) {
                hasNewCards = true;
              }
            }
          });
        }
      });

      if (hasNewCards) {
        // 检查是否需要忽略卡片检测
        if (ignoreCardDetection) {
          console.log("[计时器] 忽略卡片检测中，跳过处理");
          return;
        }

        // 清除之前的超时，实现防抖
        if (cardDetectionTimeout) {
          clearTimeout(cardDetectionTimeout);
        }

        // 延迟执行，确保所有卡片都已添加完成
        cardDetectionTimeout = setTimeout(() => {
          const cards = document.querySelectorAll(".card");
          if (cards.length > 0) {
            console.log(`检测到 ${cards.length} 个工单，开始倒计时`);
            startCountdown();
          }
        }, 100); // 100ms延迟，适应React批量更新
      }
    });

    // 监听整个文档的变化
    cardObserver.observe(document.body, {
      childList: true,
      subtree: true,
    });

    console.log("[计时器] 卡片检测已启动");
  }

  // 实时监测数据状态
  new MutationObserver(() => {
    if (isCounting && checkNoData()) {
      clearInterval(countdownInterval);
      isCounting = false;
      showTempMessage("已无数据");
    }
  }).observe(document.body, {
    childList: true,
    subtree: true,
  });

  // 初始化函数
  function init() {
    // 注册设置菜单
    GM_registerMenuCommand("⏱️ 计时器设置", createSettingsPanel);

    // 启动卡片检测
    setupCardObserver();

    console.log("[快照智能工单计时器] 初始化完成");
    console.log(`[计时器] 当前倒计时设置: ${getDelayTime()}秒`);
  }

  // 启动初始化
  if (document.readyState === "complete") {
    init();
  } else {
    window.addEventListener("load", init);
  }
})();

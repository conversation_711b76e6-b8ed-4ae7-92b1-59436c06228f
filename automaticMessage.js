(window["webpackJsonp"] = window["webpackJsonp"] || []).push([
  ["chunk-2821610e"],
  {
    "21cd": function (e, t, n) {},
    "278f": function (e, t, n) {
      "use strict";
      n("ffde");
    },
    "4897d": function (e, t, n) {},
    "51d2": function (e, t, n) {
      "use strict";
      n("96cf");
      var i = n("1da1"),
        r = n("365c");
      function a(e) {
        return s.apply(this, arguments);
      }
      function s() {
        return (
          (s = Object(i["a"])(
            regeneratorRuntime.mark(function e(t) {
              return regeneratorRuntime.wrap(function (e) {
                while (1)
                  switch ((e.prev = e.next)) {
                    case 0:
                      return e.abrupt(
                        "return",
                        Object(r["b"])("/v1/inspectapi/dispatch/config/list", t)
                      );
                    case 1:
                    case "end":
                      return e.stop();
                  }
              }, e);
            })
          )),
          s.apply(this, arguments)
        );
      }
      function o(e) {
        return c.apply(this, arguments);
      }
      function c() {
        return (
          (c = Object(i["a"])(
            regeneratorRuntime.mark(function e(t) {
              return regeneratorRuntime.wrap(function (e) {
                while (1)
                  switch ((e.prev = e.next)) {
                    case 0:
                      return e.abrupt(
                        "return",
                        Object(r["b"])(
                          "/v1/inspectapi/dispatch/config/delete",
                          t
                        )
                      );
                    case 1:
                    case "end":
                      return e.stop();
                  }
              }, e);
            })
          )),
          c.apply(this, arguments)
        );
      }
      function u(e) {
        return l.apply(this, arguments);
      }
      function l() {
        return (
          (l = Object(i["a"])(
            regeneratorRuntime.mark(function e(t) {
              return regeneratorRuntime.wrap(function (e) {
                while (1)
                  switch ((e.prev = e.next)) {
                    case 0:
                      return e.abrupt(
                        "return",
                        Object(r["b"])("/v1/inspectapi/dispatch/config/add", t)
                      );
                    case 1:
                    case "end":
                      return e.stop();
                  }
              }, e);
            })
          )),
          l.apply(this, arguments)
        );
      }
      function h(e) {
        return f.apply(this, arguments);
      }
      function f() {
        return (
          (f = Object(i["a"])(
            regeneratorRuntime.mark(function e(t) {
              return regeneratorRuntime.wrap(function (e) {
                while (1)
                  switch ((e.prev = e.next)) {
                    case 0:
                      return e.abrupt(
                        "return",
                        Object(r["b"])(
                          "/v1/inspectapi/dispatch/config/update",
                          t
                        )
                      );
                    case 1:
                    case "end":
                      return e.stop();
                  }
              }, e);
            })
          )),
          f.apply(this, arguments)
        );
      }
      function d(e) {
        return g.apply(this, arguments);
      }
      function g() {
        return (
          (g = Object(i["a"])(
            regeneratorRuntime.mark(function e(t) {
              return regeneratorRuntime.wrap(function (e) {
                while (1)
                  switch ((e.prev = e.next)) {
                    case 0:
                      return e.abrupt(
                        "return",
                        Object(r["a"])(
                          "/v1/inspectapi/dispatch/config/acquire",
                          { media_type: t }
                        )
                      );
                    case 1:
                    case "end":
                      return e.stop();
                  }
              }, e);
            })
          )),
          g.apply(this, arguments)
        );
      }
      t["a"] = {
        listDispatchConfig: a,
        deleteDispatchConfig: o,
        addDispatchConfig: u,
        updateDispatchConfig: h,
        acquireDispatchConfig: d,
      };
    },
    "63f1": function (e, t, n) {
      "use strict";
      n("21cd");
    },
    "647f": function (e, t, n) {
      "use strict";
      n("4897d");
    },
    "800d": function (e, t, n) {},
    "82bc": function (e, t, n) {
      "use strict";
      var i = function () {
          var e = this,
            t = e.$createElement,
            n = e._self._c || t;
          return e.dial_flag
            ? n("div", { staticClass: "media_box" }, [
                "img" === e.media_type
                  ? n("img", {
                      attrs: { src: e.src, crossorigin: "anonymous" },
                    })
                  : e._e(),
                "video" === e.media_type
                  ? n("video", {
                      attrs: { src: e.src, autoplay: "", loop: "" },
                    })
                  : e._e(),
                "audio" === e.media_type
                  ? n("audio", { attrs: { src: e.src, controls: "" } })
                  : e._e(),
                "text" === e.media_type
                  ? n("span", [e._v(e._s(e.src) + ">")])
                  : e._e(),
              ])
            : n("div", { staticClass: "media_box" }, [
                "img" === e.media_type
                  ? n("img", { attrs: { src: e.src } })
                  : e._e(),
                "video" === e.media_type
                  ? n("video", {
                      attrs: { src: e.src, autoplay: "", loop: "" },
                    })
                  : e._e(),
                "audio" === e.media_type
                  ? n("audio", { attrs: { src: e.src, controls: "" } })
                  : e._e(),
                "text" === e.media_type
                  ? n("span", [e._v(e._s(e.src) + ">")])
                  : e._e(),
              ]);
        },
        r = [],
        a = {
          name: "MixMedia",
          props: {
            src: { type: String },
            media_type: { type: String },
            dial_flag: { type: Boolean, default: !1 },
          },
        },
        s = a,
        o = (n("278f"), n("2877")),
        c = Object(o["a"])(s, i, r, !1, null, "3873b54e", null);
      t["a"] = c.exports;
    },
    "943c": function (e, t, n) {
      "use strict";
      n.r(t);
      var i = function () {
          var e = this,
            t = e.$createElement,
            n = e._self._c || t;
          return n(
            "a-spin",
            { attrs: { spinning: e.loading } },
            [
              n("div", [
                n(
                  "div",
                  { staticClass: "header-bar" },
                  [
                    n("div", { staticClass: "left-side word2" }, [
                      e._v(" 24h剩余快照：" + e._s(e.pendingCount) + " "),
                    ]),
                    n(
                      "div",
                      { staticClass: "middle-side" },
                      [
                        0 == e.image_list.length
                          ? n(
                              "a-button",
                              {
                                staticStyle: { "margin-right": "10px" },
                                attrs: { type: "info" },
                                on: {
                                  click: function (t) {
                                    return e.getNextBatch();
                                  },
                                },
                              },
                              [e._v("获取 ")]
                            )
                          : e._e(),
                      ],
                      1
                    ),
                    n(
                      "div",
                      { staticClass: "right-side dispatch_word" },
                      [
                        e._v(" 派单数量:" + e._s(e.batch_size) + " "),
                        n(
                          "a-button",
                          {
                            on: {
                              click: function (t) {
                                return e.refreshDispatch();
                              },
                            },
                          },
                          [e._v("设置派单")]
                        ),
                      ],
                      1
                    ),
                    n(
                      "a-modal",
                      {
                        attrs: {
                          visible: e.showSettingModal,
                          cancelText: "关闭",
                          okText: "关闭并获取",
                        },
                        on: {
                          cancel: function (t) {
                            e.showSettingModal = !1;
                          },
                          ok: e.getNextBatchAndClose,
                        },
                      },
                      [
                        n("a-checkbox-group", {
                          attrs: { name: "", options: e.flow_settings },
                          model: {
                            value: e.choose_flow_settings,
                            callback: function (t) {
                              e.choose_flow_settings = t;
                            },
                            expression: "choose_flow_settings",
                          },
                        }),
                      ],
                      1
                    ),
                  ],
                  1
                ),
                n(
                  "div",
                  {
                    staticClass: "scrollBox",
                    style: e.scrollBoxStyle,
                    attrs: { id: "scrollBox" },
                  },
                  [
                    n(
                      "div",
                      { staticClass: "row" },
                      e._l(e.image_list, function (t, i) {
                        return n("div", { key: t._id, staticClass: "card" }, [
                          n(
                            "div",
                            { staticClass: "image_card", attrs: { id: t._id } },
                            [
                              n("live-snapshot-info-pane", {
                                key: t._id,
                                attrs: {
                                  image_info: t,
                                  index: i,
                                  game_type_dict: e.game_type_dict,
                                  live_snapshot_area: e.live_snapshot_area,
                                },
                                on: { choose: e.choose },
                              }),
                            ],
                            1
                          ),
                        ]);
                      }),
                      0
                    ),
                  ]
                ),
                n(
                  "div",
                  {
                    staticClass: "block",
                    staticStyle: { width: "100%", height: "50px" },
                  },
                  [
                    n(
                      "a-form",
                      {
                        staticStyle: { "margin-right": "50px", float: "right" },
                        attrs: { layout: "inline" },
                      },
                      [
                        n(
                          "a-form-item",
                          [
                            e.image_list.length > 0
                              ? n(
                                  "a-button",
                                  {
                                    attrs: { type: "info" },
                                    on: {
                                      click: function (t) {
                                        return e.SubmitBatchCheck(!0);
                                      },
                                    },
                                  },
                                  [e._v("提交并获取 ")]
                                )
                              : e._e(),
                          ],
                          1
                        ),
                        n(
                          "a-form-item",
                          [
                            e.image_list.length > 0
                              ? n(
                                  "a-button",
                                  {
                                    attrs: { type: "info" },
                                    on: {
                                      click: function (t) {
                                        return e.SubmitBatchCheck(!1);
                                      },
                                    },
                                  },
                                  [e._v("仅提交 ")]
                                )
                              : e._e(),
                          ],
                          1
                        ),
                        n(
                          "a-form-item",
                          [
                            e.image_list.length > 0
                              ? n(
                                  "a-button",
                                  {
                                    attrs: { type: "danger" },
                                    on: {
                                      click: function (t) {
                                        return e.punishOnChoose();
                                      },
                                    },
                                  },
                                  [e._v("处罚 ")]
                                )
                              : e._e(),
                          ],
                          1
                        ),
                        n(
                          "a-form-item",
                          [
                            e.hasChoosed()
                              ? n(
                                  "a-button",
                                  {
                                    attrs: { type: "primary" },
                                    on: {
                                      click: function (t) {
                                        return e.cancel_choose();
                                      },
                                    },
                                  },
                                  [e._v(" 取消选中 ")]
                                )
                              : e._e(),
                          ],
                          1
                        ),
                      ],
                      1
                    ),
                  ],
                  1
                ),
              ]),
              n(
                "a-drawer",
                {
                  attrs: {
                    title: "处罚器",
                    width: "1200px",
                    visible: e.punisherPaneVisible,
                    "body-style": { paddingBottom: "0px" },
                  },
                  on: {
                    close: function (t) {
                      e.punisherPaneVisible = !1;
                    },
                  },
                },
                [
                  n(
                    "a-spin",
                    {
                      attrs: {
                        spinning:
                          e.userInfoLoading || e.punishmentSubmitLoading,
                      },
                    },
                    [
                      n(
                        "a-row",
                        { staticStyle: { height: "100%" } },
                        [
                          n(
                            "a-col",
                            {
                              staticStyle: {
                                height: "50vh",
                                "overflow-y": "auto",
                              },
                              attrs: { span: 6 },
                            },
                            [
                              n(
                                "a-row",
                                [
                                  n("user-info-pane", {
                                    attrs: {
                                      "user-info": e.userInfo,
                                      uid: String(e.userInfo.uid || ""),
                                      "is-anchor": !0,
                                    },
                                  }),
                                ],
                                1
                              ),
                              n(
                                "a-row",
                                { staticStyle: { "text-align": "center" } },
                                [
                                  n(
                                    "a-popover",
                                    {
                                      attrs: {
                                        visible: e.add_highrisk_show,
                                        placement: "top",
                                        title: "是否添加高风险名单？",
                                        trigger: "click",
                                      },
                                      scopedSlots: e._u([
                                        {
                                          key: "content",
                                          fn: function () {
                                            return [
                                              n(
                                                "a-row",
                                                [
                                                  n(
                                                    "a-form-model",
                                                    {
                                                      attrs: {
                                                        layout: "inline",
                                                      },
                                                    },
                                                    [
                                                      n(
                                                        "a-form-model-item",
                                                        {
                                                          attrs: {
                                                            label:
                                                              "处罚时间（分钟）",
                                                          },
                                                        },
                                                        [
                                                          n("a-input", {
                                                            staticStyle: {
                                                              width: "80px",
                                                            },
                                                            attrs: {
                                                              size: "small",
                                                            },
                                                            model: {
                                                              value:
                                                                e.add_highrisk_min,
                                                              callback:
                                                                function (t) {
                                                                  e.add_highrisk_min =
                                                                    t;
                                                                },
                                                              expression:
                                                                "add_highrisk_min",
                                                            },
                                                          }),
                                                        ],
                                                        1
                                                      ),
                                                      n(
                                                        "a-form-model-item",
                                                        {
                                                          attrs: {
                                                            label: "处罚原因",
                                                          },
                                                        },
                                                        [
                                                          n("a-input", {
                                                            staticStyle: {
                                                              width: "80px",
                                                            },
                                                            attrs: {
                                                              size: "small",
                                                            },
                                                            model: {
                                                              value:
                                                                e.add_highrisk_reason,
                                                              callback:
                                                                function (t) {
                                                                  e.add_highrisk_reason =
                                                                    t;
                                                                },
                                                              expression:
                                                                "add_highrisk_reason",
                                                            },
                                                          }),
                                                        ],
                                                        1
                                                      ),
                                                    ],
                                                    1
                                                  ),
                                                ],
                                                1
                                              ),
                                              n(
                                                "a-row",
                                                {
                                                  staticStyle: {
                                                    "text-align": "right",
                                                  },
                                                },
                                                [
                                                  n(
                                                    "a",
                                                    {
                                                      on: {
                                                        click: function (t) {
                                                          (e.add_highrisk_show =
                                                            !1),
                                                            e.addHighriskAnchor();
                                                        },
                                                      },
                                                    },
                                                    [e._v("确认")]
                                                  ),
                                                  e._v(" / "),
                                                  n(
                                                    "a",
                                                    {
                                                      on: {
                                                        click: function (t) {
                                                          e.add_highrisk_show =
                                                            !1;
                                                        },
                                                      },
                                                    },
                                                    [e._v("取消")]
                                                  ),
                                                ]
                                              ),
                                            ];
                                          },
                                          proxy: !0,
                                        },
                                      ]),
                                    },
                                    [
                                      n(
                                        "a-button",
                                        {
                                          attrs: {
                                            type: "danger",
                                            size: "small",
                                            loading: e.add_highrisk_loading,
                                            disabled:
                                              ("" !==
                                                e.is_highrisk_user.last_mtime &&
                                                "" !==
                                                  e.is_highrisk_user
                                                    .last_mtime) ||
                                              "360" === e.chose_ccid ||
                                              "669" === e.chose_ccid,
                                          },
                                          on: {
                                            click: function (t) {
                                              e.add_highrisk_show = !0;
                                            },
                                          },
                                        },
                                        [e._v(" 风险屏蔽 ")]
                                      ),
                                    ],
                                    1
                                  ),
                                ],
                                1
                              ),
                              "" !== e.is_highrisk_user.last_mtime &&
                              "" !== e.is_highrisk_user.last_mtime
                                ? n(
                                    "a-row",
                                    { staticStyle: { "text-align": "center" } },
                                    [
                                      n("span", [
                                        e._v(
                                          " " +
                                            e._s(
                                              "风险时间：" +
                                                e.is_highrisk_user.last_mtime +
                                                " - " +
                                                e.is_highrisk_user.expiretime
                                            ) +
                                            " "
                                        ),
                                      ]),
                                    ]
                                  )
                                : e._e(),
                            ],
                            1
                          ),
                          n(
                            "a-col",
                            {
                              staticStyle: {
                                height: "50vh",
                                "overflow-y": "auto",
                              },
                              attrs: { span: 18 },
                            },
                            [
                              n("simple-punishment-history", {
                                key:
                                  "simple-punish-history-" +
                                  e.userInfo.uid +
                                  "-snapshot-inspect",
                                attrs: {
                                  uid: e.userInfo.uid,
                                  "is-anchor": !0,
                                  "event-bus": e.punishEventBus,
                                },
                              }),
                            ],
                            1
                          ),
                        ],
                        1
                      ),
                      n(
                        "a-row",
                        { staticStyle: { height: "40vh" } },
                        [
                          n("anchor-punishment-pane", {
                            key:
                              "anchor-punishment-pane-" +
                              e.chose_objid +
                              "-snapshot-inspect",
                            attrs: {
                              "event-bus": e.punishEventBus,
                              evidence: e.evidence,
                              "confirm-before-punish": e.confirmBeforePunish,
                            },
                            on: {
                              "submit-punishment": e.onSubmitPunishment,
                              removeEvidence: e.removeEvidence,
                            },
                          }),
                        ],
                        1
                      ),
                    ],
                    1
                  ),
                ],
                1
              ),
              n(
                "a-modal",
                {
                  attrs: {
                    visible: e.showConfirmModal,
                    "confirm-loading": e.loading,
                    okText: "确认",
                    cancelText: "取消",
                  },
                  on: {
                    ok: function (t) {
                      return e.submitPassBatch();
                    },
                    cancel: function (t) {
                      e.showConfirmModal = !1;
                    },
                  },
                },
                [
                  e.requireNew
                    ? n("span", { staticStyle: { color: "red" } }, [
                        e._v("是否批量提交并获取？"),
                      ])
                    : n("span", { staticStyle: { color: "red" } }, [
                        e._v("是否批量提交？"),
                      ]),
                ]
              ),
            ],
            1
          );
        },
        r = [],
        a =
          (n("99af"),
          n("c975"),
          n("a15b"),
          n("d3b7"),
          n("25f0"),
          n("498a"),
          n("b85c")),
        s = (n("96cf"), n("1da1")),
        o = function () {
          var e = this,
            t = e.$createElement,
            n = e._self._c || t;
          return n(
            "div",
            { on: { mouseup: e.closeBigImg } },
            [
              n("a-spin", { attrs: { spinning: e.loading } }, [
                n(
                  "div",
                  {
                    ref: "emotionWrapper",
                    staticClass: "public_box",
                    class: e.image_info.queue_type + "_bg",
                  },
                  [
                    n(
                      "div",
                      {
                        class: e.image_info.ischeck
                          ? "choose_border"
                          : "not_choose_border",
                      },
                      [
                        e.image_info.ischeck
                          ? n("a-icon", {
                              staticStyle: {
                                "z-index": "50",
                                position: "absolute",
                                right: "0",
                                top: "0",
                                color: "#3399ff",
                                "font-size": "30px",
                              },
                              attrs: { theme: "twoTone", type: "check-square" },
                            })
                          : e._e(),
                        n(
                          "div",
                          { class: e.image_info.queue_type },
                          [
                            e.image_info.find_dial_flag
                              ? n("div", { staticClass: "dial_transbox" }, [
                                  n("p", [e._v(" 拨测")]),
                                ])
                              : e._e(),
                            n("a-row", [
                              n(
                                "div",
                                {
                                  staticClass: "public_imgbox",
                                  on: {
                                    mousedown: [
                                      function (t) {
                                        return (!t.type.indexOf("key") &&
                                          e._k(t.keyCode, "right", 39, t.key, [
                                            "Right",
                                            "ArrowRight",
                                          ])) ||
                                          ("button" in t && 2 !== t.button)
                                          ? null
                                          : e.showBigImg(t);
                                      },
                                      function (t) {
                                        return (!t.type.indexOf("key") &&
                                          e._k(t.keyCode, "left", 37, t.key, [
                                            "Left",
                                            "ArrowLeft",
                                          ])) ||
                                          ("button" in t && 0 !== t.button)
                                          ? null
                                          : e.choose();
                                      },
                                    ],
                                  },
                                },
                                [
                                  n("div", { staticClass: "media_box" }, [
                                    n("img", {
                                      ref: "snapshot_img_tag",
                                      attrs: {
                                        src: e.image_info.url,
                                        crossorigin: "anonymous",
                                      },
                                    }),
                                  ]),
                                ]
                              ),
                              e.image_info.hasPunished
                                ? n("div", { staticClass: "reject_transbox" }, [
                                    n("p", [e._v(" 已处罚")]),
                                  ])
                                : e._e(),
                            ]),
                            n(
                              "div",
                              { staticStyle: { margin: "3px", width: "100%" } },
                              [
                                n(
                                  "a-row",
                                  {
                                    staticStyle: {
                                      display: "flex",
                                      "justify-content": "flex-end",
                                      width: "98%",
                                    },
                                  },
                                  [
                                    n("a-col", [
                                      e._v(
                                        e._s(e.timestamp2time(e.image_info.ts))
                                      ),
                                    ]),
                                    n(
                                      "a-col",
                                      {
                                        staticStyle: { "margin-left": "auto" },
                                      },
                                      [
                                        n(
                                          "span",
                                          {
                                            staticStyle: { color: "deeppink" },
                                            attrs: {
                                              title: e.get_full_game_type_name(
                                                e.image_info.gametype
                                              ),
                                            },
                                          },
                                          [
                                            e._v(
                                              " " +
                                                e._s(
                                                  e.get_game_type_name(
                                                    e.image_info.gametype
                                                  )
                                                )
                                            ),
                                          ]
                                        ),
                                      ]
                                    ),
                                  ],
                                  1
                                ),
                                n(
                                  "a-row",
                                  {
                                    staticStyle: {
                                      display: "flex",
                                      "justify-content": "flex-end",
                                      width: "98%",
                                    },
                                  },
                                  [
                                    n("a-col", [
                                      e._v("昵称: "),
                                      n(
                                        "a",
                                        {
                                          attrs: { target: "_blank" },
                                          on: {
                                            click: function (t) {
                                              return e.show_user_info();
                                            },
                                          },
                                        },
                                        [
                                          e._v(
                                            e._s(
                                              e.get_fixed_length_text(
                                                e.image_info.nickname,
                                                10
                                              )
                                            )
                                          ),
                                        ]
                                      ),
                                    ]),
                                    n(
                                      "a-col",
                                      [
                                        e.image_info.remark
                                          ? n(
                                              "a-popover",
                                              { attrs: { title: "备注" } },
                                              [
                                                n(
                                                  "template",
                                                  { slot: "content" },
                                                  [
                                                    e._v(
                                                      e._s(e.image_info.remark)
                                                    ),
                                                  ]
                                                ),
                                                n(
                                                  "a-button",
                                                  {
                                                    staticStyle: {
                                                      "font-size": "6px",
                                                    },
                                                    attrs: {
                                                      type: "danger",
                                                      size: "small",
                                                    },
                                                  },
                                                  [e._v("备 ")]
                                                ),
                                              ],
                                              2
                                            )
                                          : e._e(),
                                      ],
                                      1
                                    ),
                                    n(
                                      "a-col",
                                      {
                                        staticStyle: { "margin-left": "auto" },
                                      },
                                      [
                                        n(
                                          "span",
                                          {
                                            staticStyle: { color: "#2B333F" },
                                            attrs: {
                                              title: e.get_risk_label(
                                                e.image_info
                                              ),
                                            },
                                          },
                                          [
                                            e._v(
                                              " " +
                                                e._s(
                                                  e.get_fixed_length_text(
                                                    e.get_risk_label(
                                                      e.image_info
                                                    ),
                                                    8
                                                  )
                                                )
                                            ),
                                          ]
                                        ),
                                      ]
                                    ),
                                    n("a-col", [
                                      n(
                                        "div",
                                        {
                                          staticStyle: {
                                            "margin-left": "10px",
                                            color: "darkred",
                                          },
                                        },
                                        [
                                          e._v(
                                            "房间: " + e._s(e.image_info.roomid)
                                          ),
                                        ]
                                      ),
                                    ]),
                                  ],
                                  1
                                ),
                                n(
                                  "a-row",
                                  { staticStyle: { display: "flex" } },
                                  [
                                    n("a-col", [
                                      n(
                                        "span",
                                        {
                                          attrs: { title: e.image_info.title },
                                        },
                                        [
                                          e._v(
                                            "标题: " +
                                              e._s(
                                                e.get_fixed_length_text(
                                                  e.image_info.title,
                                                  14
                                                )
                                              )
                                          ),
                                        ]
                                      ),
                                    ]),
                                  ],
                                  1
                                ),
                                n(
                                  "a-row",
                                  {
                                    attrs: {
                                      type: "flex",
                                      justify: "space-around",
                                      align: "middle",
                                    },
                                  },
                                  [
                                    n(
                                      "a-col",
                                      [
                                        n(
                                          "a-button",
                                          {
                                            staticStyle: {
                                              height: "18px",
                                              "font-size": "6px",
                                            },
                                            attrs: { type: "info" },
                                            on: { click: e.onGotoLiveRoom },
                                          },
                                          [e._v(" 飞机票")]
                                        ),
                                      ],
                                      1
                                    ),
                                    n(
                                      "a-col",
                                      [
                                        n(
                                          "a-button",
                                          {
                                            staticStyle: {
                                              height: "18px",
                                              "font-size": "6px",
                                            },
                                            attrs: { type: "info" },
                                            on: {
                                              click: function (t) {
                                                return e.refreshImg();
                                              },
                                            },
                                          },
                                          [e._v("刷新")]
                                        ),
                                      ],
                                      1
                                    ),
                                    n(
                                      "a-col",
                                      [
                                        n(
                                          "a-button",
                                          {
                                            staticStyle: {
                                              height: "18px",
                                              "font-size": "6px",
                                            },
                                            attrs: { type: "info" },
                                            on: {
                                              click: function (t) {
                                                return e.copyUrl(
                                                  e.image_info.url
                                                );
                                              },
                                            },
                                          },
                                          [e._v("复制url")]
                                        ),
                                      ],
                                      1
                                    ),
                                    n(
                                      "a-col",
                                      [
                                        n(
                                          "a-button",
                                          {
                                            staticStyle: {
                                              height: "18px",
                                              "font-size": "6px",
                                            },
                                            attrs: { type: "info" },
                                            on: {
                                              click: function (t) {
                                                return e.showHistory();
                                              },
                                            },
                                          },
                                          [e._v(" 历史截图")]
                                        ),
                                      ],
                                      1
                                    ),
                                  ],
                                  1
                                ),
                              ],
                              1
                            ),
                          ],
                          1
                        ),
                      ],
                      1
                    ),
                  ]
                ),
              ]),
              n(
                "el-dialog",
                {
                  attrs: {
                    width: "772px",
                    visible: e.bigImgVisible,
                    modal: !1,
                    "show-close": !1,
                  },
                  on: {
                    "update:visible": function (t) {
                      e.bigImgVisible = t;
                    },
                  },
                },
                [
                  n("div", { staticClass: "big_image" }, [
                    n("img", {
                      staticStyle: { "min-width": "702px", height: "405px" },
                      attrs: { src: e.image_info.url },
                    }),
                  ]),
                ]
              ),
              n(
                "el-dialog",
                {
                  attrs: { visible: e.userInfoVisible },
                  on: {
                    "update:visible": function (t) {
                      e.userInfoVisible = t;
                    },
                  },
                },
                [
                  n("user-info-pane", {
                    attrs: {
                      "user-info": e.userInfo,
                      uid: String(e.userInfo.uid || ""),
                      "is-anchor": !0,
                    },
                  }),
                ],
                1
              ),
              n(
                "el-dialog",
                {
                  attrs: {
                    width: "1420px",
                    visible: e.historyVisible,
                    modal: !1,
                    title: "历史截图",
                  },
                  on: {
                    "update:visible": function (t) {
                      e.historyVisible = t;
                    },
                  },
                },
                [
                  n(
                    "span",
                    { attrs: { slot: "title" }, slot: "title" },
                    [
                      n("span", [e._v("注：选中历史截图即加入举证 ")]),
                      n(
                        "el-button",
                        {
                          staticStyle: { "margin-left": "100px" },
                          attrs: { type: "primary" },
                          on: {
                            click: function (t) {
                              return e.openLivePlayback();
                            },
                          },
                        },
                        [
                          n("a-icon", { attrs: { type: "video-camera" } }),
                          e._v("直播回放"),
                        ],
                        1
                      ),
                      e.isChooseHistory()
                        ? n(
                            "el-button",
                            {
                              staticStyle: { "margin-left": "100px" },
                              on: {
                                click: function (t) {
                                  return e.cancelChooseHistory();
                                },
                              },
                            },
                            [e._v("取消选中")]
                          )
                        : e._e(),
                      n(
                        "el-button",
                        {
                          staticStyle: { "margin-left": "100px" },
                          attrs: { type: "primary" },
                          on: {
                            click: function (t) {
                              return e.ChooseAllHistory();
                            },
                          },
                        },
                        [e._v("全选整页")]
                      ),
                    ],
                    1
                  ),
                  n("div", { staticClass: "scroll_dialog" }, [
                    n(
                      "div",
                      { staticClass: "row" },
                      e._l(e.image_info.historyList, function (t, i) {
                        return n("div", { key: t._id, staticClass: "card" }, [
                          i % e.lineNum == 0
                            ? n(
                                "div",
                                { staticClass: "fixed_button" },
                                [
                                  n(
                                    "a-button",
                                    {
                                      staticClass: "history_image_line_button",
                                      attrs: { size: "small", type: "primary" },
                                      on: {
                                        click: function (t) {
                                          return e.chooseHistoryLine(
                                            i,
                                            i + e.lineNum
                                          );
                                        },
                                      },
                                    },
                                    [e._v(" 全选此行 ")]
                                  ),
                                  n(
                                    "a-button",
                                    {
                                      staticClass: "history_image_line_button",
                                      attrs: { size: "small", type: "primary" },
                                      on: {
                                        click: function (t) {
                                          return e.cancelHistoryLine(
                                            i,
                                            i + e.lineNum
                                          );
                                        },
                                      },
                                    },
                                    [e._v(" 取消此行 ")]
                                  ),
                                ],
                                1
                              )
                            : e._e(),
                          n(
                            "div",
                            {
                              staticClass: "history_image_card",
                              attrs: { id: t._id },
                            },
                            [
                              n("live-snapshot-history-pane", {
                                key: t._id,
                                attrs: { image_info: t, index: i },
                                on: { choose: e.chooseHistory },
                              }),
                            ],
                            1
                          ),
                        ]);
                      }),
                      0
                    ),
                  ]),
                ]
              ),
            ],
            1
          );
        },
        c = [],
        u =
          (n("caad"),
          n("b0c0"),
          n("a9e3"),
          n("3ca3"),
          n("ddb0"),
          n("2b3d"),
          n("4484")),
        l = n("d2e1"),
        h = n("cadb"),
        f = n("bdd9"),
        d = function () {
          var e = this,
            t = e.$createElement,
            n = e._self._c || t;
          return n(
            "div",
            { on: { mouseup: e.closeBigImg } },
            [
              n("a-spin", { attrs: { spinning: e.loading } }, [
                n("div", { ref: "emotionWrapper", staticClass: "public_box" }, [
                  n(
                    "div",
                    {
                      class: e.image_info.ischeck
                        ? "choose_border"
                        : "not_choose_border",
                    },
                    [
                      e.image_info.ischeck
                        ? n("a-icon", {
                            staticStyle: {
                              "z-index": "100",
                              position: "absolute",
                              right: "0",
                              top: "0",
                              color: "#3399ff",
                              "font-size": "30px",
                            },
                            attrs: { theme: "twoTone", type: "check-square" },
                          })
                        : e._e(),
                      n(
                        "div",
                        { class: e.image_info.queue_type },
                        [
                          n("a-row", [
                            n(
                              "div",
                              {
                                staticClass: "public_imgbox",
                                on: {
                                  mousedown: [
                                    function (t) {
                                      return (!t.type.indexOf("key") &&
                                        e._k(t.keyCode, "right", 39, t.key, [
                                          "Right",
                                          "ArrowRight",
                                        ])) ||
                                        ("button" in t && 2 !== t.button)
                                        ? null
                                        : e.showBigImg();
                                    },
                                    function (t) {
                                      return (!t.type.indexOf("key") &&
                                        e._k(t.keyCode, "left", 37, t.key, [
                                          "Left",
                                          "ArrowLeft",
                                        ])) ||
                                        ("button" in t && 0 !== t.button)
                                        ? null
                                        : e.choose();
                                    },
                                  ],
                                },
                              },
                              [
                                n("mix-media", {
                                  attrs: {
                                    media_type: "img",
                                    src: e.image_info.url,
                                  },
                                }),
                              ],
                              1
                            ),
                          ]),
                          n(
                            "div",
                            { staticStyle: { "margin-left": "5px" } },
                            [
                              n(
                                "a-row",
                                { staticStyle: { display: "flex" } },
                                [
                                  n("a-col", [
                                    e._v(
                                      e._s(e.timestamp2time(e.image_info.ts))
                                    ),
                                  ]),
                                ],
                                1
                              ),
                            ],
                            1
                          ),
                        ],
                        1
                      ),
                    ],
                    1
                  ),
                ]),
              ]),
              n(
                "el-dialog",
                {
                  attrs: {
                    width: "702px",
                    visible: e.bigImgVisible,
                    modal: !1,
                  },
                  on: {
                    "update:visible": function (t) {
                      e.bigImgVisible = t;
                    },
                  },
                },
                [
                  n(
                    "div",
                    { staticClass: "big_image" },
                    [
                      n("mix-media", {
                        attrs: { media_type: "img", src: e.image_info.url },
                      }),
                    ],
                    1
                  ),
                ]
              ),
            ],
            1
          );
        },
        g = [],
        m = n("82bc"),
        p = {
          name: "LiveSnapshotHistoryPane",
          components: { MixMedia: m["a"] },
          props: {
            image_info: { type: Object, default: null },
            index: { type: Number, default: 0 },
            image_origin: { type: String, default: "" },
          },
          mounted: function () {
            return Object(s["a"])(
              regeneratorRuntime.mark(function e() {
                return regeneratorRuntime.wrap(function (e) {
                  while (1)
                    switch ((e.prev = e.next)) {
                      case 0:
                        window.oncontextmenu = function (e) {
                          e.preventDefault();
                        };
                      case 1:
                      case "end":
                        return e.stop();
                    }
                }, e);
              })
            )();
          },
          data: function () {
            return { loading: !1, bigImgVisible: !1 };
          },
          computed: {},
          methods: {
            timestamp2time: h["c"],
            onLoad: function () {
              var e = new Image();
              e.src = this.image_info.url;
              var t = 0;
              (e.fileSize > 0 || (e.width > 1 && e.height > 1)) &&
                (t = e.width + 40),
                t < this.minWidth && (t = this.minWidth),
                (this.width = t + "px");
            },
            showBigImg: function () {
              this.bigImgVisible = !0;
            },
            closeBigImg: function () {
              this.bigImgVisible = !1;
            },
            get_state_info: function (e) {
              return e in this.audit_state ? this.audit_state[e] : e;
            },
            choose: function () {
              this.image_info.ischeck
                ? (this.image_info.ischeck = !1)
                : (this.image_info.ischeck = !0),
                this.$emit("choose", {
                  image_info: this.image_info,
                  index: this.index,
                });
            },
            warn: function (e) {
              var t = "";
              e.response &&
                e.response.data &&
                (t =
                  e.response.data.msg ||
                  e.response.data.why ||
                  e.response.data.code),
                t || (t = e.message || e || "unknown error"),
                this.$message.error("失败报错: ".concat(t), 3);
            },
          },
        },
        _ = p,
        v = (n("9eb0"), n("d83c"), n("2877")),
        b = Object(v["a"])(_, d, g, !1, null, "1bfb359c", null),
        y = b.exports,
        w = {
          name: "InspectInfoPane",
          components: { UserInfoPane: l["a"], LiveSnapshotHistoryPane: y },
          props: {
            image_info: { type: Object, default: null },
            index: { type: Number, default: 0 },
            game_type_dict: { type: Object, default: null },
            live_snapshot_area: { type: Object, default: null },
          },
          created: function () {},
          beforeDestroy: function () {},
          mounted: function () {
            (this.modifiedImgURL = (this.image_info || {}).url || ""),
              this.refreshImg(),
              (window.oncontextmenu = function (e) {
                e.preventDefault();
              });
          },
          data: function () {
            return {
              loading: !1,
              bigImgVisible: !1,
              infoVisible: !1,
              userInfoVisible: !1,
              width: "500px",
              minWidth: 500,
              emotion_info: {},
              audit_state: { 0: "待审核", 1: "审核通过", 2: "审核不通过" },
              ischeck: !1,
              userInfo: {},
              historyVisible: !1,
              lineNum: 4,
              modifiedImgURL: "",
              proxyIndex: 0,
            };
          },
          computed: {},
          methods: {
            timestamp2time: h["c"],
            onLoad: function () {
              var e = new Image();
              e.src = this.image_info.url;
              var t = 0;
              (e.fileSize > 0 || (e.width > 1 && e.height > 1)) &&
                (t = e.width + 40),
                t < this.minWidth && (t = this.minWidth),
                (this.width = t + "px");
            },
            get_ch: function (e) {
              return null != e && void 0 != e && e && this.live_snapshot_area[e]
                ? this.live_snapshot_area[e]
                : e;
            },
            openLivePlayback: function () {
              var e = this.image_info.uid,
                t = Object(h["c"])(this.image_info.ts),
                n = Object(h["d"])(this.image_info.ts, -5, "minutes"),
                i = { id: e, idtype: "uid", startTime: n, endTime: t };
              this.$router.push({ name: "playback", query: { form: i } });
            },
            showHistory: function () {
              var e = this;
              return Object(s["a"])(
                regeneratorRuntime.mark(function t() {
                  var n, i, r, s, o;
                  return regeneratorRuntime.wrap(
                    function (t) {
                      while (1)
                        switch ((t.prev = t.next)) {
                          case 0:
                            if (!e.check_dial_test()) {
                              t.next = 2;
                              break;
                            }
                            return t.abrupt("return");
                          case 2:
                            if (
                              ((t.prev = 2),
                              !(e.image_info.historyList.length > 0))
                            ) {
                              t.next = 6;
                              break;
                            }
                            return (e.historyVisible = !0), t.abrupt("return");
                          case 6:
                            return (
                              (t.next = 8),
                              f["a"].get30History(e.image_info._id)
                            );
                          case 8:
                            (n = t.sent),
                              (i = n.data.result),
                              (r = Object(a["a"])(i));
                            try {
                              for (r.s(); !(s = r.n()).done; )
                                (o = s.value),
                                  (o.isHistory = !1),
                                  (o.ischeck = !1),
                                  (o.hasPunished = !1);
                            } catch (c) {
                              r.e(c);
                            } finally {
                              r.f();
                            }
                            (e.image_info.historyList = i),
                              e.image_info.historyList.length > 0
                                ? (e.historyVisible = !0)
                                : e.$message.warning("查无历史截图"),
                              (t.next = 19);
                            break;
                          case 16:
                            (t.prev = 16), (t.t0 = t["catch"](2)), e.warn(t.t0);
                          case 19:
                          case "end":
                            return t.stop();
                        }
                    },
                    t,
                    null,
                    [[2, 16]]
                  );
                })
              )();
            },
            get_risk_label: function (e) {
              for (
                var t = [
                    { key: "lougou_label", name: "漏沟" },
                    { key: "ocr_label", name: "OCR命中" },
                    { key: "politics_label", name: "涉政" },
                    { key: "yellowish_label", name: "涉黄" },
                    { key: "hangup_label", name: "挂机" },
                  ],
                  n = "",
                  i = 0,
                  r = t;
                i < r.length;
                i++
              ) {
                var a = r[i];
                e[a.key] && (n += a.name + " ");
              }
              var s = { blacklist: "黑名单" };
              return s[e.queue_type] && (n += s[e.queue_type]), n;
            },
            get_fixed_length_text: function (e, t) {
              return e && e.length > t ? e.substring(0, t) + "..." : e;
            },
            get_game_type_name: function (e) {
              if (null != e && void 0 !== e && e) {
                var t = "";
                return (
                  (t = e in this.game_type_dict ? this.game_type_dict[e] : e),
                  t.length > 12 ? t.substring(0, 12) + "..." : t
                );
              }
              return e;
            },
            get_full_game_type_name: function (e) {
              if (null != e && void 0 !== e && e) {
                var t = "";
                return (
                  (t = e in this.game_type_dict ? this.game_type_dict[e] : e), t
                );
              }
              return e;
            },
            show_user_info: function () {
              var e = this;
              return Object(s["a"])(
                regeneratorRuntime.mark(function t() {
                  return regeneratorRuntime.wrap(
                    function (t) {
                      while (1)
                        switch ((t.prev = t.next)) {
                          case 0:
                            return (
                              (e.loading = !0),
                              (t.prev = 1),
                              (t.next = 4),
                              e.getUserInfo()
                            );
                          case 4:
                            (e.userInfoVisible = !0), (t.next = 10);
                            break;
                          case 7:
                            (t.prev = 7), (t.t0 = t["catch"](1)), e.warn(t.t0);
                          case 10:
                            return (
                              (t.prev = 10), (e.loading = !1), t.finish(10)
                            );
                          case 13:
                          case "end":
                            return t.stop();
                        }
                    },
                    t,
                    null,
                    [[1, 7, 10, 13]]
                  );
                })
              )();
            },
            getUserInfo: function () {
              var e = this;
              return Object(s["a"])(
                regeneratorRuntime.mark(function t() {
                  var n, i, r, a, s;
                  return regeneratorRuntime.wrap(
                    function (t) {
                      while (1)
                        switch ((t.prev = t.next)) {
                          case 0:
                            return (
                              (n = "uid"),
                              (i = e.image_info.uid),
                              (t.prev = 2),
                              (r = "anchor"),
                              (t.next = 6),
                              u["a"].getUserInfo(n, i, r)
                            );
                          case 6:
                            if (((a = t.sent), "OK" === a.code)) {
                              t.next = 10;
                              break;
                            }
                            return (
                              e.$message.error("加载数据失败 ".concat(a.msg)),
                              t.abrupt("return")
                            );
                          case 10:
                            (e.userInfo = a.data.uinfo || {}), (t.next = 19);
                            break;
                          case 13:
                            (t.prev = 13),
                              (t.t0 = t["catch"](2)),
                              (s = ""),
                              t.t0.response &&
                                t.t0.response.data &&
                                (s =
                                  t.t0.response.data.msg ||
                                  t.t0.response.data.why ||
                                  t.t0.response.data.code),
                              s ||
                                (s = t.t0.message || t.t0 || "unknown error"),
                              e.$message.error("获取数据失败: ".concat(s), 3);
                          case 19:
                          case "end":
                            return t.stop();
                        }
                    },
                    t,
                    null,
                    [[2, 13]]
                  );
                })
              )();
            },
            showBigImg: function () {
              this.bigImgVisible = !0;
            },
            closeBigImg: function () {
              this.bigImgVisible = !1;
            },
            showInfo: function () {
              this.infoVisible = !0;
            },
            check_dial_test: function () {
              return (
                !!this.image_info.dial_flag &&
                ((this.image_info.ischeck = !1),
                (this.image_info.hasPunished = !1),
                (this.image_info.find_dial_flag = !0),
                this.$message.info("很棒，发现了拨测", 3),
                !0)
              );
            },
            onGotoLiveRoom: function () {
              if (!this.check_dial_test()) {
                var e = "https://cc.163.com/"
                  .concat(this.image_info.roomid, "/")
                  .concat(this.image_info.channelid);
                window.open(e, "_blank");
              }
            },
            cancelChooseHistory: function () {
              var e,
                t = Object(a["a"])(this.image_info.historyList);
              try {
                for (t.s(); !(e = t.n()).done; ) {
                  var n = e.value;
                  n.ischeck && (n.ischeck = !1);
                }
              } catch (i) {
                t.e(i);
              } finally {
                t.f();
              }
            },
            chooseHistoryLine: function (e, t) {
              for (var n = e; n < t; n++)
                n < this.image_info.historyList.length &&
                  (this.image_info.historyList[n].ischeck = !0);
              this.chooseHistory({
                image_info: this.image_info,
                index: this.index,
              });
            },
            cancelHistoryLine: function (e, t) {
              for (var n = e; n < t; n++)
                n < this.image_info.historyList.length &&
                  (this.image_info.historyList[n].ischeck = !1);
            },
            ChooseAllHistory: function () {
              var e,
                t = Object(a["a"])(this.image_info.historyList);
              try {
                for (t.s(); !(e = t.n()).done; ) {
                  var n = e.value;
                  n.ischeck = !0;
                }
              } catch (i) {
                t.e(i);
              } finally {
                t.f();
              }
              this.chooseHistory({
                image_info: this.image_info,
                index: this.index,
              });
            },
            isChooseHistory: function () {
              var e,
                t = Object(a["a"])(this.image_info.historyList);
              try {
                for (t.s(); !(e = t.n()).done; ) {
                  var n = e.value;
                  if (n.ischeck) return !0;
                }
              } catch (i) {
                t.e(i);
              } finally {
                t.f();
              }
              return !1;
            },
            choose: function () {
              this.image_info.ischeck && this.isChooseHistory()
                ? this.$message.warning(
                    "快照有关联历史截图作为举证，无法取消勾选"
                  )
                : this.$emit("choose", {
                    image_info: this.image_info,
                    index: this.index,
                  });
            },
            chooseHistory: function (e) {
              e.image_info, e.index;
              this.image_info.ischeck ||
                this.$emit("choose", {
                  image_info: this.image_info,
                  index: this.index,
                });
            },
            warn: function (e) {
              var t = "";
              e.response &&
                e.response.data &&
                (t =
                  e.response.data.msg ||
                  e.response.data.why ||
                  e.response.data.code),
                t || (t = e.message || e || "unknown error"),
                this.$message.error("失败报错: ".concat(t), 3);
            },
            copyUrl: function (e) {
              var t = document.createElement("input");
              (t.value = e),
                document.body.appendChild(t),
                t.select(),
                document.execCommand("Copy"),
                document.body.removeChild(t),
                this.$message.success("复制成功");
            },
            refreshImg: function () {
              var e = this.image_info || {},
                t = e.dial_flag,
                n = this.$refs["snapshot_img_tag"],
                i = n.src || (this.image_info || {}).url || "";
              if (i && i.length) {
                var r = new URL(
                  /^\/\//.test(i) ? window.location.protocol + i : i
                );
                if ((r.searchParams.set("_random", new Date().getTime()), t)) {
                  var a = r.hostname,
                    s = [
                      "//liveproxy1-inspect.cc.163.com",
                      "//liveproxy2-inspect.cc.163.com",
                      "//liveproxy3-inspect.cc.163.com",
                      "//liveproxy4-inspect.cc.163.com",
                    ];
                  if (s.includes("//".concat(a))) {
                    var o,
                      c,
                      u =
                        null === (o = r.searchParams) || void 0 === o
                          ? void 0
                          : o.get("upstream"),
                      l =
                        null === (c = r.searchParams) || void 0 === c
                          ? void 0
                          : c.get("host");
                    u &&
                      u.length &&
                      (i = ""
                        .concat(
                          s[this.proxyIndex++ % s.length],
                          "/imgproxy?_random="
                        )
                        .concat(new Date().getTime(), "&host=")
                        .concat(l, "&upstream=")
                        .concat(u));
                  } else
                    i = ""
                      .concat(
                        s[this.proxyIndex++ % s.length],
                        "/imgproxy?_random="
                      )
                      .concat(new Date().getTime(), "&host=")
                      .concat(a, "&upstream=")
                      .concat(i);
                } else i = r.toString();
                n.src = i;
              }
            },
          },
        },
        k = w,
        x =
          (n("647f"),
          n("bc09"),
          Object(v["a"])(k, o, c, !1, null, "8c180582", null)),
        O = x.exports,
        R = n("51d2"),
        j = n("af14"),
        C = n("9b01"),
        S = n("2b0e"),
        I = n("e089"),
        L = {
          name: "inspect",
          components: {
            LiveSnapshotInfoPane: O,
            AnchorPunishmentPane: j["a"],
            UserInfoPane: l["a"],
            SimplePunishmentHistory: C["a"],
          },
          data: function () {
            return {
              pendingCount: 0,
              loading: !1,
              userInfoLoading: !1,
              punishmentSubmitLoading: !1,
              showSettingModal: !1,
              submitWaitTimer: null,
              batch_size: 200,
              game_type_dict: {},
              flow_settings: [],
              choose_flow_settings: [],
              all_flow_settings: [],
              image_list: [],
              live_snapshot_area: {
                highrisk: "高危队列",
                superr: "超R主播",
                blacklist: "黑名单队列",
                hangup: "挂机队列",
                fullamount: "全量队列",
                othernew: "小新游全量",
              },
              flow_setting_list: [],
              userInfo: {},
              evidence: {},
              punishEventBus: new S["default"](),
              image_type_map: {},
              rowCount: 8,
              showConfirmModal: !1,
              showConfirmModal2: !1,
              headParHeight: 175,
              punisherPaneVisible: !1,
              scrollBoxStyle: { height: "1000px", overflow: "scroll" },
              requireNew: !1,
              canSubmitOnSpace: !0,
              has_data: !1,
              snapshotLockTimer: null,
              lockCount: 0,
              releaseTime: 600,
              chose_objid: "",
              chose_ccid: "",
              add_highrisk_loading: !1,
              add_highrisk_show: !1,
              add_highrisk_min: 10,
              add_highrisk_reason: "",
              is_highrisk_user: { expiretime: "", last_mtime: "" },
            };
          },
          beforeCreate: function () {
            var e = this;
            window.onresize = function () {
              return (function () {
                e.get_scroll_height();
              })();
            };
          },
          created: function () {
            var e = this;
            this.get_scroll_height(),
              this.getReleaseTime(),
              window.addEventListener(
                "beforeunload",
                Object(s["a"])(
                  regeneratorRuntime.mark(function t() {
                    return regeneratorRuntime.wrap(function (t) {
                      while (1)
                        switch ((t.prev = t.next)) {
                          case 0:
                            return (t.next = 2), e.releaseInspectLock();
                          case 2:
                          case "end":
                            return t.stop();
                        }
                    }, t);
                  })
                )
              );
          },
          mounted: function () {
            var e = this;
            return Object(s["a"])(
              regeneratorRuntime.mark(function t() {
                return regeneratorRuntime.wrap(function (t) {
                  while (1)
                    switch ((t.prev = t.next)) {
                      case 0:
                        return (t.next = 2), e.get_game_type();
                      case 2:
                        return (t.next = 4), e.getDispatchConfig();
                      case 4:
                        return (t.next = 6), e.getPendingCounts();
                      case 6:
                        return (t.next = 8), e.scheduleFunc();
                      case 8:
                      case "end":
                        return t.stop();
                    }
                }, t);
              })
            )();
          },
          beforeRouteUpdate: (function () {
            var e = Object(s["a"])(
              regeneratorRuntime.mark(function e(t, n, i) {
                return regeneratorRuntime.wrap(
                  function (e) {
                    while (1)
                      switch ((e.prev = e.next)) {
                        case 0:
                          return (e.next = 2), this.getDispatchConfig();
                        case 2:
                          return (e.next = 4), this.getPendingCounts();
                        case 4:
                          window.addEventListener("keypress", this.HotKeyEvent),
                            i();
                        case 6:
                        case "end":
                          return e.stop();
                      }
                  },
                  e,
                  this
                );
              })
            );
            function t(t, n, i) {
              return e.apply(this, arguments);
            }
            return t;
          })(),
          beforeRouteLeave: function (e, t, n) {
            window.removeEventListener("keypress", this.HotKeyEvent), n();
          },
          beforeRouteEnter: function (e, t, n) {
            n(function (e) {
              window.addEventListener("keypress", e.HotKeyEvent);
            });
          },
          beforeDestroy: function () {
            var e = this;
            return Object(s["a"])(
              regeneratorRuntime.mark(function t() {
                return regeneratorRuntime.wrap(function (t) {
                  while (1)
                    switch ((t.prev = t.next)) {
                      case 0:
                        return (
                          window.removeEventListener("keypress", e.HotKeyEvent),
                          (t.next = 3),
                          e.releaseInspectLock()
                        );
                      case 3:
                      case "end":
                        return t.stop();
                    }
                }, t);
              })
            )();
          },
          destroyed: function () {
            var e = this;
            return Object(s["a"])(
              regeneratorRuntime.mark(function t() {
                return regeneratorRuntime.wrap(function (t) {
                  while (1)
                    switch ((t.prev = t.next)) {
                      case 0:
                        return (t.next = 2), e.releaseInspectLock();
                      case 2:
                      case "end":
                        return t.stop();
                    }
                }, t);
              })
            )();
          },
          computed: {
            confirmBeforePunish: function () {
              var e = !1;
              return (
                this.userInfo.remark &&
                  this.userInfo.remark.length &&
                  this.userInfo.remark.trim().length &&
                  (e = !0),
                "s_level" === this.userInfo.virtual_level && (e = !0),
                e
              );
            },
          },
          watch: {
            add_highrisk_show: function (e) {
              this.add_highrisk_min = 10;
            },
          },
          methods: {
            get_scroll_height: function () {
              this.scrollBoxStyle.height =
                window.innerHeight - this.headParHeight + "px";
            },
            get_game_type: function () {
              var e = this;
              return Object(s["a"])(
                regeneratorRuntime.mark(function t() {
                  var n;
                  return regeneratorRuntime.wrap(
                    function (t) {
                      while (1)
                        switch ((t.prev = t.next)) {
                          case 0:
                            return (
                              (t.prev = 0), (t.next = 3), f["a"].get_gametype()
                            );
                          case 3:
                            (n = t.sent),
                              (e.game_type_dict = n.data),
                              (t.next = 10);
                            break;
                          case 7:
                            (t.prev = 7), (t.t0 = t["catch"](0)), e.warn(t.t0);
                          case 10:
                          case "end":
                            return t.stop();
                        }
                    },
                    t,
                    null,
                    [[0, 7]]
                  );
                })
              )();
            },
            cancel_choose: function () {
              var e,
                t = Object(a["a"])(this.image_list);
              try {
                for (t.s(); !(e = t.n()).done; ) {
                  var n,
                    i = e.value,
                    r = Object(a["a"])(i.historyList);
                  try {
                    for (r.s(); !(n = r.n()).done; ) {
                      var s = n.value;
                      s.ischeck = !1;
                    }
                  } catch (o) {
                    r.e(o);
                  } finally {
                    r.f();
                  }
                  i.ischeck = !1;
                }
              } catch (o) {
                t.e(o);
              } finally {
                t.f();
              }
            },
            hasChoosed: function () {
              var e,
                t = Object(a["a"])(this.image_list);
              try {
                for (t.s(); !(e = t.n()).done; ) {
                  var n = e.value;
                  if (n.ischeck) return !0;
                }
              } catch (i) {
                t.e(i);
              } finally {
                t.f();
              }
              return !1;
            },
            choose: function (e) {
              var t = e.image_info,
                n = e.index;
              if (t.ischeck) t.ischeck = !1;
              else {
                var i = this.getChooseUid();
                if (-1 === i || t.uid === i)
                  (t.index = n), (i = t.uid), (t.ischeck = !0);
                else {
                  if (
                    (this.$message.warning(
                      "已选中截图的主播UID【"
                        .concat(i, "】和此UID【")
                        .concat(t.uid, "】不一致，请检查")
                    ),
                    t.historyList)
                  ) {
                    var r,
                      s = Object(a["a"])(t.historyList);
                    try {
                      for (s.s(); !(r = s.n()).done; ) {
                        var o = r.value;
                        o.ischeck = !1;
                      }
                    } catch (c) {
                      s.e(c);
                    } finally {
                      s.f();
                    }
                  }
                  t.ischeck = !1;
                }
              }
            },
            timestamp2time: h["c"],
            timestamp2time_add: h["d"],
            GenNonDuplicateID: function () {
              return Math.random().toString();
            },
            getChooseList: function () {
              var e,
                t = [],
                n = Object(a["a"])(this.image_list);
              try {
                for (n.s(); !(e = n.n()).done; ) {
                  var i = e.value;
                  if (i.ischeck) {
                    (i["uuid"] = this.GenNonDuplicateID()), t.push(i);
                    var r,
                      s = Object(a["a"])(i.historyList);
                    try {
                      for (s.s(); !(r = s.n()).done; ) {
                        var o = r.value;
                        o.ischeck &&
                          ((o["uuid"] = this.GenNonDuplicateID()), t.push(o));
                      }
                    } catch (c) {
                      s.e(c);
                    } finally {
                      s.f();
                    }
                  }
                }
              } catch (c) {
                n.e(c);
              } finally {
                n.f();
              }
              return t;
            },
            getChooseUid: function () {
              var e,
                t = Object(a["a"])(this.image_list);
              try {
                for (t.s(); !(e = t.n()).done; ) {
                  var n = e.value;
                  if (n.ischeck)
                    return (
                      (this.chose_objid = n._id),
                      (this.chose_ccid = n.ccid),
                      n.uid
                    );
                }
              } catch (i) {
                t.e(i);
              } finally {
                t.f();
              }
              return -1;
            },
            punishOnChoose: function () {
              var e = this.getChooseUid();
              -1 !== e
                ? (this.getUserInfo("uid", e),
                  (this.evidence = {
                    type: "live_snapshots",
                    url_ex: !0,
                    content: this.getChooseList(),
                  }),
                  (this.punisherPaneVisible = !0))
                : this.$message.warning(
                    "未选择快照，无指定主播账号，请鼠标点击图片选择"
                  );
            },
            choosePunishSucc: function () {
              var e = this;
              return Object(s["a"])(
                regeneratorRuntime.mark(function t() {
                  var n, i, r, s, o, c, u;
                  return regeneratorRuntime.wrap(function (t) {
                    while (1)
                      switch ((t.prev = t.next)) {
                        case 0:
                          (n = e.getChooseList()),
                            (i = Object(a["a"])(e.image_list));
                          try {
                            for (i.s(); !(r = i.n()).done; )
                              if (((s = r.value), s.ischeck)) {
                                (s.hasPunished = !0),
                                  (s.ischeck = !1),
                                  (o = Object(a["a"])(s.historyList));
                                try {
                                  for (o.s(); !(c = o.n()).done; )
                                    (u = c.value),
                                      u.ischeck &&
                                        ((u.hasPunished = !0),
                                        (u.ischeck = !1));
                                } catch (l) {
                                  o.e(l);
                                } finally {
                                  o.f();
                                }
                              }
                          } catch (l) {
                            i.e(l);
                          } finally {
                            i.f();
                          }
                          return (t.next = 5), e.submitChoosePunish(n);
                        case 5:
                        case "end":
                          return t.stop();
                      }
                  }, t);
                })
              )();
            },
            getUserInfo: function (e, t) {
              var n = this;
              return Object(s["a"])(
                regeneratorRuntime.mark(function i() {
                  var r, a, s;
                  return regeneratorRuntime.wrap(
                    function (i) {
                      while (1)
                        switch ((i.prev = i.next)) {
                          case 0:
                            return (
                              (i.prev = 0),
                              (n.userInfoLoading = !0),
                              (r = "anchor"),
                              (i.next = 5),
                              u["a"].getUserInfo(e, t, r)
                            );
                          case 5:
                            if (((a = i.sent), "OK" === a.code)) {
                              i.next = 9;
                              break;
                            }
                            return (
                              n.$message.error("加载数据失败 ".concat(a.msg)),
                              i.abrupt("return")
                            );
                          case 9:
                            (n.userInfo = a.data.uinfo || {}), (i.next = 18);
                            break;
                          case 12:
                            (i.prev = 12),
                              (i.t0 = i["catch"](0)),
                              (s = ""),
                              i.t0.response &&
                                i.t0.response.data &&
                                (s =
                                  i.t0.response.data.msg ||
                                  i.t0.response.data.why ||
                                  i.t0.response.data.code),
                              s ||
                                (s = i.t0.message || i.t0 || "unknown error"),
                              n.$message.error("获取数据失败: ".concat(s), 3);
                          case 18:
                            return (
                              (i.prev = 18),
                              (n.userInfoLoading = !1),
                              i.finish(18)
                            );
                          case 21:
                            return (i.next = 23), n.isHighriskAnchor();
                          case 23:
                          case "end":
                            return i.stop();
                        }
                    },
                    i,
                    null,
                    [[0, 12, 18, 21]]
                  );
                })
              )();
            },
            getPendingCounts: function () {
              var e = this;
              return Object(s["a"])(
                regeneratorRuntime.mark(function t() {
                  var n, i, r;
                  return regeneratorRuntime.wrap(
                    function (t) {
                      while (1)
                        switch ((t.prev = t.next)) {
                          case 0:
                            if (
                              ((t.prev = 0), e.$store.getters.allowAcquired)
                            ) {
                              t.next = 3;
                              break;
                            }
                            return t.abrupt("return");
                          case 3:
                            if (!(e.all_flow_settings.length <= 0)) {
                              t.next = 6;
                              break;
                            }
                            return (
                              e.$message.error(
                                "可选配置为空，请联系组长配置派单"
                              ),
                              t.abrupt("return")
                            );
                          case 6:
                            if (!(e.choose_flow_settings.length <= 0)) {
                              t.next = 9;
                              break;
                            }
                            return (
                              (e.showSettingModal = !0), t.abrupt("return")
                            );
                          case 9:
                            return (
                              (n = {
                                choose_flow_settings: e.choose_flow_settings,
                                all_flow_settings: e.all_flow_settings,
                              }),
                              (t.next = 12),
                              f["a"].getPendingCount(n)
                            );
                          case 12:
                            (i = t.sent),
                              (r = i.data),
                              (e.pendingCount = r.result),
                              (t.next = 20);
                            break;
                          case 17:
                            (t.prev = 17), (t.t0 = t["catch"](0)), e.warn(t.t0);
                          case 20:
                          case "end":
                            return t.stop();
                        }
                    },
                    t,
                    null,
                    [[0, 17]]
                  );
                })
              )();
            },
            getNextBatchAndClose: function () {
              var e = this;
              return Object(s["a"])(
                regeneratorRuntime.mark(function t() {
                  return regeneratorRuntime.wrap(function (t) {
                    while (1)
                      switch ((t.prev = t.next)) {
                        case 0:
                          return (
                            (e.showSettingModal = !1),
                            (t.next = 3),
                            e.getNextBatch()
                          );
                        case 3:
                        case "end":
                          return t.stop();
                      }
                  }, t);
                })
              )();
            },
            refreshDispatch: function () {
              var e = this;
              return Object(s["a"])(
                regeneratorRuntime.mark(function t() {
                  return regeneratorRuntime.wrap(function (t) {
                    while (1)
                      switch ((t.prev = t.next)) {
                        case 0:
                          return (t.next = 2), e.getDispatchConfig();
                        case 2:
                          (e.showSettingModal = !0), e.getPendingCounts();
                        case 4:
                        case "end":
                          return t.stop();
                      }
                  }, t);
                })
              )();
            },
            getDispatchConfig: function () {
              var e = this;
              return Object(s["a"])(
                regeneratorRuntime.mark(function t() {
                  var n, i, r, s, o, c, u, l, h, f;
                  return regeneratorRuntime.wrap(
                    function (t) {
                      while (1)
                        switch ((t.prev = t.next)) {
                          case 0:
                            return (
                              (t.prev = 0),
                              (t.next = 3),
                              R["a"].acquireDispatchConfig("live_snapshot")
                            );
                          case 3:
                            if (
                              ((n = t.sent),
                              (i = n.data.config.config),
                              (r = i.flow_settings),
                              (s = []),
                              r)
                            ) {
                              o = Object(a["a"])(r);
                              try {
                                for (o.s(); !(c = o.n()).done; )
                                  (u = c.value), s.push({ label: u, value: u });
                              } catch (d) {
                                o.e(d);
                              } finally {
                                o.f();
                              }
                              e.batch_size = i.batch_size;
                            } else e.$message.error("派单配置为空");
                            (e.flow_settings = s),
                              (e.all_flow_settings = r),
                              (l = Object(a["a"])(e.choose_flow_settings));
                            try {
                              for (l.s(); !(h = l.n()).done; )
                                (f = h.value),
                                  -1 == r.indexOf(f) &&
                                    ((e.choose_flow_settings = []),
                                    e.$message.info(
                                      "派单配置已经更新，请重新选择"
                                    ),
                                    (e.showSettingModal = !0));
                            } catch (d) {
                              l.e(d);
                            } finally {
                              l.f();
                            }
                            t.next = 16;
                            break;
                          case 14:
                            (t.prev = 14), (t.t0 = t["catch"](0));
                          case 16:
                          case "end":
                            return t.stop();
                        }
                    },
                    t,
                    null,
                    [[0, 14]]
                  );
                })
              )();
            },
            getNextBatch: function () {
              var e = this;
              return Object(s["a"])(
                regeneratorRuntime.mark(function t() {
                  var n, i, r, o, c, u, l;
                  return regeneratorRuntime.wrap(
                    function (t) {
                      while (1)
                        switch ((t.prev = t.next)) {
                          case 0:
                            if (e.$store.getters.allowAcquired) {
                              t.next = 3;
                              break;
                            }
                            return (
                              e.$message.info("请先签入再开始工作吧ಠ·ಠ", 3),
                              t.abrupt("return")
                            );
                          case 3:
                            return (t.next = 5), e.getDispatchConfig();
                          case 5:
                            if (!(e.all_flow_settings.length <= 0)) {
                              t.next = 8;
                              break;
                            }
                            return (
                              e.$message.error(
                                "可选配置为空，请联系组长配置派单"
                              ),
                              t.abrupt("return")
                            );
                          case 8:
                            if (!(e.choose_flow_settings.length <= 0)) {
                              t.next = 11;
                              break;
                            }
                            return (
                              (e.showSettingModal = !0), t.abrupt("return")
                            );
                          case 11:
                            return (
                              (e.loading = !0),
                              (t.prev = 12),
                              (n = {
                                choose_flow_settings: e.choose_flow_settings,
                                all_flow_settings: e.all_flow_settings,
                                batch_size: e.batch_size,
                              }),
                              (t.next = 16),
                              f["a"].getNextBatch(n)
                            );
                          case 16:
                            (i = t.sent),
                              (r = i.data.result),
                              (o = Object(a["a"])(r));
                            try {
                              for (o.s(); !(c = o.n()).done; )
                                (u = c.value),
                                  (u.ischeck = !1),
                                  (u.find_dial_flag = !1),
                                  (u.hasPunished = !1),
                                  (u.historyList = []),
                                  (u.chooseHistoryList = []);
                            } catch (h) {
                              o.e(h);
                            } finally {
                              o.f();
                            }
                            (e.image_list = r), (t.next = 26);
                            break;
                          case 23:
                            (t.prev = 23),
                              (t.t0 = t["catch"](12)),
                              e.warn(t.t0);
                          case 26:
                            (e.loading = !1),
                              e.getPendingCounts(),
                              0 == e.image_list.length &&
                                e.$message.info("暂无待审核数据"),
                              e.image_list.length > 0 &&
                                ((e.canSubmitOnSpace = !1),
                                (l = 20 * e.image_list.length),
                                (e.submitWaitTimer = setTimeout(
                                  Object(s["a"])(
                                    regeneratorRuntime.mark(function t() {
                                      return regeneratorRuntime.wrap(function (
                                        t
                                      ) {
                                        while (1)
                                          switch ((t.prev = t.next)) {
                                            case 0:
                                              e.canSubmitOnSpace = !0;
                                            case 1:
                                            case "end":
                                              return t.stop();
                                          }
                                      },
                                      t);
                                    })
                                  ),
                                  l
                                )));
                          case 30:
                          case "end":
                            return t.stop();
                        }
                    },
                    t,
                    null,
                    [[12, 23]]
                  );
                })
              )();
            },
            check_has_dial_and_set_screen_url: function () {
              var e = this;
              return Object(s["a"])(
                regeneratorRuntime.mark(function t() {
                  var n, i, r, s, o, c, u, l;
                  return regeneratorRuntime.wrap(
                    function (t) {
                      while (1)
                        switch ((t.prev = t.next)) {
                          case 0:
                            return (t.next = 2), e.$nextTick();
                          case 2:
                            (n = !1),
                              (i = Object(a["a"])(e.image_list)),
                              (t.prev = 4),
                              i.s();
                          case 6:
                            if ((r = i.n()).done) {
                              t.next = 13;
                              break;
                            }
                            if (((s = r.value), !s.dial_flag)) {
                              t.next = 11;
                              break;
                            }
                            return (n = !0), t.abrupt("break", 13);
                          case 11:
                            t.next = 6;
                            break;
                          case 13:
                            t.next = 18;
                            break;
                          case 15:
                            (t.prev = 15), (t.t0 = t["catch"](4)), i.e(t.t0);
                          case 18:
                            return (t.prev = 18), i.f(), t.finish(18);
                          case 21:
                            if (!n) {
                              t.next = 29;
                              break;
                            }
                            return new Date(), (t.next = 25), Object(I["a"])();
                          case 25:
                            (o = t.sent), (c = Object(a["a"])(e.image_list));
                            try {
                              for (c.s(); !(u = c.n()).done; )
                                (l = u.value),
                                  l.dial_flag && (l["screen_url"] = o);
                            } catch (h) {
                              c.e(h);
                            } finally {
                              c.f();
                            }
                            new Date();
                          case 29:
                          case "end":
                            return t.stop();
                        }
                    },
                    t,
                    null,
                    [[4, 15, 18, 21]]
                  );
                })
              )();
            },
            submitPassBatch: function () {
              var e = this;
              return Object(s["a"])(
                regeneratorRuntime.mark(function t() {
                  var n, i, r, s, o, c;
                  return regeneratorRuntime.wrap(
                    function (t) {
                      while (1)
                        switch ((t.prev = t.next)) {
                          case 0:
                            return (
                              (t.prev = 0),
                              (n = []),
                              (t.next = 4),
                              e.submit_dial_test()
                            );
                          case 4:
                            i = Object(a["a"])(e.image_list);
                            try {
                              for (i.s(); !(r = i.n()).done; )
                                (s = r.value),
                                  s.hasPunished || s.dial_flag || n.push(s._id);
                            } catch (u) {
                              i.e(u);
                            } finally {
                              i.f();
                            }
                            if (!(n.length > 0)) {
                              t.next = 18;
                              break;
                            }
                            return (
                              (o = { state: 1, ids: n }),
                              (t.next = 10),
                              f["a"].submitGroup(o)
                            );
                          case 10:
                            if (((c = t.sent), !c.data.result)) {
                              t.next = 16;
                              break;
                            }
                            e.$message.info("提交成功"),
                              (e.lockCount = 0),
                              (t.next = 18);
                            break;
                          case 16:
                            return (
                              e.$message.info("提交失败"), t.abrupt("return")
                            );
                          case 18:
                            if (((e.image_list = []), !e.requireNew)) {
                              t.next = 24;
                              break;
                            }
                            return (t.next = 22), e.getNextBatch();
                          case 22:
                            t.next = 25;
                            break;
                          case 24:
                            e.getPendingCounts();
                          case 25:
                            t.next = 30;
                            break;
                          case 27:
                            (t.prev = 27), (t.t0 = t["catch"](0)), e.warn(t.t0);
                          case 30:
                            return (
                              (t.prev = 30),
                              (e.showConfirmModal = !1),
                              t.finish(30)
                            );
                          case 33:
                          case "end":
                            return t.stop();
                        }
                    },
                    t,
                    null,
                    [[0, 27, 30, 33]]
                  );
                })
              )();
            },
            submitChoosePunish: function (e) {
              return Object(s["a"])(
                regeneratorRuntime.mark(function t() {
                  var n, i, r, s, o;
                  return regeneratorRuntime.wrap(function (t) {
                    while (1)
                      switch ((t.prev = t.next)) {
                        case 0:
                          (n = []), (i = Object(a["a"])(e));
                          try {
                            for (i.s(); !(r = i.n()).done; )
                              (s = r.value), n.push(s._id);
                          } catch (c) {
                            i.e(c);
                          } finally {
                            i.f();
                          }
                          if (0 != n.length) {
                            t.next = 5;
                            break;
                          }
                          return t.abrupt("return");
                        case 5:
                          return (
                            (o = { state: 0, ids: n }),
                            (t.next = 8),
                            f["a"].submitGroup(o)
                          );
                        case 8:
                        case "end":
                          return t.stop();
                      }
                  }, t);
                })
              )();
            },
            removeEvidence: function (e) {
              var t,
                n = e.image_info,
                i = (e.index, Object(a["a"])(this.image_list));
              try {
                for (i.s(); !(t = i.n()).done; ) {
                  var r = t.value;
                  if (r.ischeck) {
                    if (r.uuid === n.uuid) {
                      r.ischeck = !1;
                      var s,
                        o = Object(a["a"])(r.historyList);
                      try {
                        for (o.s(); !(s = o.n()).done; ) {
                          var c = s.value;
                          c.ischeck = !1;
                        }
                      } catch (f) {
                        o.e(f);
                      } finally {
                        o.f();
                      }
                    }
                    var u,
                      l = Object(a["a"])(r.historyList);
                    try {
                      for (l.s(); !(u = l.n()).done; ) {
                        var h = u.value;
                        h.ischeck && h.uuid === n.uuid && (h.ischeck = !1);
                      }
                    } catch (f) {
                      l.e(f);
                    } finally {
                      l.f();
                    }
                  }
                }
              } catch (f) {
                i.e(f);
              } finally {
                i.f();
              }
              this.evidence = {
                type: "live_snapshots",
                url_ex: !0,
                content: this.getChooseList(),
              };
            },
            find_dial: function (e) {
              var t = e.image_info,
                n = e.index;
              (t.ischeck = !1),
                (t.hasPunished = !1),
                (t.find_dial_flag = !0),
                (this.image_list[n].ischeck = !1),
                (this.image_list[n].hasPunished = !1),
                (this.image_list[n].find_dial_flag = !0);
            },
            check_dial_test: function () {
              var e,
                t = !1,
                n = this.getChooseList(),
                i = Object(a["a"])(n);
              try {
                for (i.s(); !(e = i.n()).done; ) {
                  var r = e.value;
                  r.dial_flag &&
                    ((this.image_list[r.index].ischeck = !1),
                    (this.image_list[r.index].hasPunished = !1),
                    (this.image_list[r.index].find_dial_flag = !0),
                    (t = !0));
                }
              } catch (s) {
                i.e(s);
              } finally {
                i.f();
              }
              return t && this.$message.info("很棒，发现了拨测", 3), t;
            },
            submit_dial_test: function () {
              var e = this;
              return Object(s["a"])(
                regeneratorRuntime.mark(function t() {
                  var n, i, r, s, o, c;
                  return regeneratorRuntime.wrap(
                    function (t) {
                      while (1)
                        switch ((t.prev = t.next)) {
                          case 0:
                            (n = []),
                              (i = []),
                              (r = Object(a["a"])(e.image_list));
                            try {
                              for (r.s(); !(s = r.n()).done; )
                                (o = s.value),
                                  o.dial_flag &&
                                    (o.find_dial_flag ? n.push(o) : i.push(o));
                            } catch (u) {
                              r.e(u);
                            } finally {
                              r.f();
                            }
                            return (
                              (t.prev = 4),
                              (c = {
                                find: n,
                                not_find: i,
                                all_num: e.image_list.length,
                              }),
                              (t.next = 8),
                              f["a"].submit_live_dial_test(c)
                            );
                          case 8:
                            t.next = 13;
                            break;
                          case 10:
                            (t.prev = 10), (t.t0 = t["catch"](4)), e.warn(t.t0);
                          case 13:
                          case "end":
                            return t.stop();
                        }
                    },
                    t,
                    null,
                    [[4, 10]]
                  );
                })
              )();
            },
            onSubmitPunishment: function (e) {
              var t = this;
              return Object(s["a"])(
                regeneratorRuntime.mark(function n() {
                  var i, r, a, s, o, c, l, h, f;
                  return regeneratorRuntime.wrap(
                    function (n) {
                      while (1)
                        switch ((n.prev = n.next)) {
                          case 0:
                            if (
                              ((i = e.role),
                              (r = e.isCustom),
                              (a = e.select),
                              (s = e.custom),
                              !t.check_dial_test())
                            ) {
                              n.next = 4;
                              break;
                            }
                            return (
                              (t.punisherPaneVisible = !1), n.abrupt("return")
                            );
                          case 4:
                            if (
                              ((t.loading = !0),
                              (n.prev = 5),
                              (t.punishmentSubmitLoading = !0),
                              (o = t.getChooseUid()),
                              -1 !== t.getChooseUid())
                            ) {
                              n.next = 11;
                              break;
                            }
                            return (
                              t.$message.warning("未选择快照，无指定主播账号"),
                              n.abrupt("return")
                            );
                          case 11:
                            if (o) {
                              n.next = 14;
                              break;
                            }
                            return (
                              t.$message.warning("未指定或未找到主播账号"),
                              n.abrupt("return")
                            );
                          case 14:
                            return (
                              (c = t.getChooseList()),
                              (t.evidence = {
                                type: "live_snapshots",
                                url_ex: !0,
                                content: c,
                              }),
                              (l = c[0].gametype),
                              (n.next = 19),
                              u["a"].submitPunishment2nd(
                                o,
                                t.userInfo.nickname,
                                i,
                                l,
                                r,
                                "live_snapshot",
                                a,
                                s,
                                t.evidence
                              )
                            );
                          case 19:
                            if (((h = n.sent), "OK" === h.code)) {
                              n.next = 23;
                              break;
                            }
                            return (
                              t.punishEventBus.$emit("onPunishmentSubmitted", {
                                succeeded: !1,
                                message: "转二审失败：".concat(h.msg),
                              }),
                              n.abrupt("return")
                            );
                          case 23:
                            return (
                              t.punishEventBus.$emit("onPunishmentSubmitted", {
                                succeeded: !0,
                                message: "已转二审",
                              }),
                              (n.next = 26),
                              t.choosePunishSucc()
                            );
                          case 26:
                            (t.hasPunished = !0), (n.next = 36);
                            break;
                          case 29:
                            (n.prev = 29),
                              (n.t0 = n["catch"](5)),
                              (f = ""),
                              n.t0.response &&
                                n.t0.response.data &&
                                (f =
                                  n.t0.response.data.msg ||
                                  n.t0.response.data.why ||
                                  n.t0.response.data.code),
                              !f &&
                                n.t0 &&
                                (f = n.t0.msg || n.t0.why || n.t0.code),
                              f ||
                                (f = n.t0.message || n.t0 || "unknown error"),
                              t.punishEventBus.$emit("onPunishmentSubmitted", {
                                succeeded: !1,
                                message: "处罚失败：提交数据出错 ".concat(f),
                              });
                          case 36:
                            return (
                              (n.prev = 36),
                              (t.loading = !1),
                              (t.punishmentSubmitLoading = !1),
                              n.finish(36)
                            );
                          case 40:
                          case "end":
                            return n.stop();
                        }
                    },
                    n,
                    null,
                    [[5, 29, 36, 40]]
                  );
                })
              )();
            },
            HotKeyEvent: function (e) {
              "Space" == e.code
                ? this.isScrollBottom() &&
                  (this.showConfirmModal ||
                    ((this.requireNew = !0), this.SubmitBatchCheck(!0)))
                : "KeyF" == e.code
                ? this.showConfirmModal && this.submitPassBatch()
                : "KeyS" == e.code &&
                  (document.getElementById("scrollBox").scrollTop += 900);
            },
            isScrollBottom: function () {
              var e =
                  document.getElementById("scrollBox").scrollTop ||
                  document.body.scrollTop,
                t =
                  document.getElementById("scrollBox").clientHeight ||
                  document.body.clientHeight,
                n =
                  document.getElementById("scrollBox").scrollHeight ||
                  document.body.scrollHeight;
              return e + t >= n;
            },
            SubmitBatchCheck: function (e) {
              var t = this;
              return Object(s["a"])(
                regeneratorRuntime.mark(function n() {
                  return regeneratorRuntime.wrap(function (n) {
                    while (1)
                      switch ((n.prev = n.next)) {
                        case 0:
                          if (0 != t.image_list.length) {
                            n.next = 4;
                            break;
                          }
                          return (n.next = 3), t.getNextBatch();
                        case 3:
                          return n.abrupt("return");
                        case 4:
                          if (!t.showConfirmModal) {
                            n.next = 8;
                            break;
                          }
                          return (n.next = 7), t.submitPassBatch();
                        case 7:
                          return n.abrupt("return");
                        case 8:
                          t.canSubmitOnSpace
                            ? ((t.showConfirmModal = !0), (t.requireNew = e))
                            : t.$message.error("请不要频繁提交获取哦(>▽<)", 1);
                        case 9:
                        case "end":
                          return n.stop();
                      }
                  }, n);
                })
              )();
            },
            warn: function (e) {
              var t = "";
              e.response &&
                e.response.data &&
                (t =
                  e.response.data.msg ||
                  e.response.data.why ||
                  e.response.data.code),
                t || (t = e.message || e || "unknown error"),
                this.$message.error("获取数据失败: ".concat(t), 3);
            },
            getReleaseTime: function () {
              var e = this;
              f["a"].get_releasetime().then(function (t) {
                t.data.result &&
                  (e.releaseTime = t.data.result["release_over_time_sec"]);
              });
            },
            scheduleFunc: function () {
              var e = this;
              return Object(s["a"])(
                regeneratorRuntime.mark(function t() {
                  var n;
                  return regeneratorRuntime.wrap(function (t) {
                    while (1)
                      switch ((t.prev = t.next)) {
                        case 0:
                          if (
                            ((n = e.scheduleFunc),
                            setTimeout(
                              Object(s["a"])(
                                regeneratorRuntime.mark(function e() {
                                  return regeneratorRuntime.wrap(function (e) {
                                    while (1)
                                      switch ((e.prev = e.next)) {
                                        case 0:
                                          return (e.next = 2), n();
                                        case 2:
                                        case "end":
                                          return e.stop();
                                      }
                                  }, e);
                                })
                              ),
                              1e4
                            ),
                            e.image_list.length > 0 && (e.lockCount += 1),
                            !(10 * e.lockCount > e.releaseTime))
                          ) {
                            t.next = 6;
                            break;
                          }
                          return (t.next = 6), e.releaseInspectLock();
                        case 6:
                        case "end":
                          return t.stop();
                      }
                  }, t);
                })
              )();
            },
            releaseInspectLockTest: function () {
              var e = this;
              return Object(s["a"])(
                regeneratorRuntime.mark(function t() {
                  return regeneratorRuntime.wrap(function (t) {
                    while (1)
                      switch ((t.prev = t.next)) {
                        case 0:
                          e.lockCount = 0;
                        case 1:
                        case "end":
                          return t.stop();
                      }
                  }, t);
                })
              )();
            },
            releaseInspectLock: function () {
              var e = this;
              return Object(s["a"])(
                regeneratorRuntime.mark(function t() {
                  var n, i, r, a, s, o;
                  return regeneratorRuntime.wrap(function (t) {
                    while (1)
                      switch ((t.prev = t.next)) {
                        case 0:
                          if (
                            (e.snapshotLockTimer &&
                              clearTimeout(e.snapshotLockTimer),
                            0 !== e.image_list.length)
                          ) {
                            t.next = 3;
                            break;
                          }
                          return t.abrupt("return");
                        case 3:
                          try {
                            for (i in ((n = []), e.image_list))
                              (r = e.image_list[i]),
                                (a = r["_id"]),
                                (s = r["hasPunished"]),
                                a &&
                                  (!1 === s && n.push(a),
                                  (o = document.getElementById(a)),
                                  o && o.remove());
                            n.length > 0 &&
                              f["a"]
                                .release_record(n.join(","))
                                .then(function (t) {
                                  "success" === t.data.result &&
                                    (e.$message.success("审核超时释放"),
                                    (e.punisherPaneVisible = !1));
                                }),
                              (e.image_list = []),
                              (e.lockCount = 0);
                          } catch (c) {}
                        case 4:
                        case "end":
                          return t.stop();
                      }
                  }, t);
                })
              )();
            },
            addHighriskAnchor: function () {
              var e = this;
              this.add_highrisk_loading = !0;
              var t = parseInt(this.add_highrisk_min);
              if (!t)
                return (
                  this.$message.error("处罚时间必须是正整数"),
                  void (this.add_highrisk_loading = !1)
                );
              u["a"]
                .addHighriskAnchor(
                  this.userInfo.urs,
                  "URS",
                  "S-Sexrisk",
                  this.add_highrisk_reason,
                  this.add_highrisk_min
                )
                .then(function (t) {
                  "success" === t.data.result
                    ? (e.$message.success("添加成功"), e.isHighriskAnchor())
                    : e.$message.error("添加失败");
                })
                .finally(function () {
                  e.add_highrisk_loading = !1;
                });
            },
            isHighriskAnchor: function () {
              var e = this;
              return Object(s["a"])(
                regeneratorRuntime.mark(function t() {
                  return regeneratorRuntime.wrap(function (t) {
                    while (1)
                      switch ((t.prev = t.next)) {
                        case 0:
                          u["a"]
                            .isHighriskAnchor(
                              e.userInfo.urs,
                              "URS",
                              "S-Sexrisk"
                            )
                            .then(function (t) {
                              e.is_highrisk_user = t.data.result;
                            })
                            .catch(function (t) {
                              e.$message.error("报错了：" + t);
                            });
                        case 1:
                        case "end":
                          return t.stop();
                      }
                  }, t);
                })
              )();
            },
          },
        },
        P = L,
        B = (n("63f1"), Object(v["a"])(P, i, r, !1, null, "77f4c326", null));
      t["default"] = B.exports;
    },
    "97c1": function (e, t, n) {},
    "9eb0": function (e, t, n) {
      "use strict";
      n("97c1");
    },
    aff5: function (e, t, n) {
      var i = n("23e7");
      i({ target: "Number", stat: !0 }, { MAX_SAFE_INTEGER: 9007199254740991 });
    },
    bc09: function (e, t, n) {
      "use strict";
      n("800d");
    },
    bdd9: function (e, t, n) {
      "use strict";
      n("96cf");
      var i = n("1da1"),
        r = n("365c"),
        a = (function () {
          var e = Object(i["a"])(
            regeneratorRuntime.mark(function e(t) {
              return regeneratorRuntime.wrap(function (e) {
                while (1)
                  switch ((e.prev = e.next)) {
                    case 0:
                      return e.abrupt(
                        "return",
                        Object(r["b"])(
                          "/v1/inspectapi/live_snapshot/get_pending_count",
                          t
                        )
                      );
                    case 1:
                    case "end":
                      return e.stop();
                  }
              }, e);
            })
          );
          return function (t) {
            return e.apply(this, arguments);
          };
        })(),
        s = (function () {
          var e = Object(i["a"])(
            regeneratorRuntime.mark(function e(t) {
              return regeneratorRuntime.wrap(function (e) {
                while (1)
                  switch ((e.prev = e.next)) {
                    case 0:
                      return e.abrupt(
                        "return",
                        Object(r["b"])(
                          "/v1/inspectapi/live_snapshot/get_batch",
                          t
                        )
                      );
                    case 1:
                    case "end":
                      return e.stop();
                  }
              }, e);
            })
          );
          return function (t) {
            return e.apply(this, arguments);
          };
        })(),
        o = (function () {
          var e = Object(i["a"])(
            regeneratorRuntime.mark(function e(t) {
              return regeneratorRuntime.wrap(function (e) {
                while (1)
                  switch ((e.prev = e.next)) {
                    case 0:
                      return e.abrupt(
                        "return",
                        Object(r["b"])(
                          "/v1/inspectapi/live_snapshot/get_batch_on_query",
                          t
                        )
                      );
                    case 1:
                    case "end":
                      return e.stop();
                  }
              }, e);
            })
          );
          return function (t) {
            return e.apply(this, arguments);
          };
        })(),
        c = (function () {
          var e = Object(i["a"])(
            regeneratorRuntime.mark(function e(t) {
              return regeneratorRuntime.wrap(function (e) {
                while (1)
                  switch ((e.prev = e.next)) {
                    case 0:
                      return e.abrupt(
                        "return",
                        Object(r["b"])(
                          "/v1/inspectapi/live_snapshot/submit_group_state",
                          t
                        )
                      );
                    case 1:
                    case "end":
                      return e.stop();
                  }
              }, e);
            })
          );
          return function (t) {
            return e.apply(this, arguments);
          };
        })(),
        u = (function () {
          var e = Object(i["a"])(
            regeneratorRuntime.mark(function e(t) {
              return regeneratorRuntime.wrap(function (e) {
                while (1)
                  switch ((e.prev = e.next)) {
                    case 0:
                      return e.abrupt(
                        "return",
                        Object(r["a"])(
                          "/v1/inspectapi/live_snapshot/get_history",
                          { id: t }
                        )
                      );
                    case 1:
                    case "end":
                      return e.stop();
                  }
              }, e);
            })
          );
          return function (t) {
            return e.apply(this, arguments);
          };
        })(),
        l = (function () {
          var e = Object(i["a"])(
            regeneratorRuntime.mark(function e() {
              return regeneratorRuntime.wrap(function (e) {
                while (1)
                  switch ((e.prev = e.next)) {
                    case 0:
                      return e.abrupt(
                        "return",
                        Object(r["a"])(
                          "/v1/inspectapi/live_snapshot/get_gametype",
                          {}
                        )
                      );
                    case 1:
                    case "end":
                      return e.stop();
                  }
              }, e);
            })
          );
          return function () {
            return e.apply(this, arguments);
          };
        })(),
        h = (function () {
          var e = Object(i["a"])(
            regeneratorRuntime.mark(function e(t) {
              return regeneratorRuntime.wrap(function (e) {
                while (1)
                  switch ((e.prev = e.next)) {
                    case 0:
                      return e.abrupt(
                        "return",
                        Object(r["b"])(
                          "/v1/inspectapi/live_snapshot/change_expire_url",
                          t
                        )
                      );
                    case 1:
                    case "end":
                      return e.stop();
                  }
              }, e);
            })
          );
          return function (t) {
            return e.apply(this, arguments);
          };
        })(),
        f = (function () {
          var e = Object(i["a"])(
            regeneratorRuntime.mark(function e() {
              return regeneratorRuntime.wrap(function (e) {
                while (1)
                  switch ((e.prev = e.next)) {
                    case 0:
                      return e.abrupt(
                        "return",
                        Object(r["a"])(
                          "/v1/inspectapi/live_snapshot/get_flow_list",
                          {}
                        )
                      );
                    case 1:
                    case "end":
                      return e.stop();
                  }
              }, e);
            })
          );
          return function () {
            return e.apply(this, arguments);
          };
        })(),
        d = (function () {
          var e = Object(i["a"])(
            regeneratorRuntime.mark(function e() {
              return regeneratorRuntime.wrap(function (e) {
                while (1)
                  switch ((e.prev = e.next)) {
                    case 0:
                      return e.abrupt(
                        "return",
                        Object(r["a"])(
                          "/v1/inspectapi/live_snapshot/get_flow_setting_list",
                          {}
                        )
                      );
                    case 1:
                    case "end":
                      return e.stop();
                  }
              }, e);
            })
          );
          return function () {
            return e.apply(this, arguments);
          };
        })(),
        g = (function () {
          var e = Object(i["a"])(
            regeneratorRuntime.mark(function e(t) {
              return regeneratorRuntime.wrap(function (e) {
                while (1)
                  switch ((e.prev = e.next)) {
                    case 0:
                      return e.abrupt(
                        "return",
                        Object(r["b"])(
                          "/v1/inspectapi/live_snapshot/submit_live_dial_test",
                          t
                        )
                      );
                    case 1:
                    case "end":
                      return e.stop();
                  }
              }, e);
            })
          );
          return function (t) {
            return e.apply(this, arguments);
          };
        })(),
        m = (function () {
          var e = Object(i["a"])(
            regeneratorRuntime.mark(function e() {
              return regeneratorRuntime.wrap(function (e) {
                while (1)
                  switch ((e.prev = e.next)) {
                    case 0:
                      return e.abrupt(
                        "return",
                        Object(r["a"])(
                          "/v1/inspectapi/live_snapshot/get_releasetime",
                          {}
                        )
                      );
                    case 1:
                    case "end":
                      return e.stop();
                  }
              }, e);
            })
          );
          return function () {
            return e.apply(this, arguments);
          };
        })(),
        p = (function () {
          var e = Object(i["a"])(
            regeneratorRuntime.mark(function e(t) {
              return regeneratorRuntime.wrap(function (e) {
                while (1)
                  switch ((e.prev = e.next)) {
                    case 0:
                      return e.abrupt(
                        "return",
                        Object(r["a"])(
                          "/v1/inspectapi/live_snapshot/release_record",
                          { ids: t }
                        )
                      );
                    case 1:
                    case "end":
                      return e.stop();
                  }
              }, e);
            })
          );
          return function (t) {
            return e.apply(this, arguments);
          };
        })();
      t["a"] = {
        getPendingCount: a,
        getNextBatch: s,
        queryBatch: o,
        get30History: u,
        submitGroup: c,
        get_gametype: l,
        get_flow_list: f,
        get_flow_setting_list: d,
        change_expire_url: h,
        submit_live_dial_test: g,
        get_releasetime: m,
        release_record: p,
      };
    },
    cadb: function (e, t, n) {
      "use strict";
      n.d(t, "a", function () {
        return a;
      }),
        n.d(t, "c", function () {
          return s;
        }),
        n.d(t, "d", function () {
          return o;
        }),
        n.d(t, "b", function () {
          return c;
        });
      n("a9e3"), n("aff5"), n("d3b7"), n("25f0");
      var i = n("c1df"),
        r = n.n(i);
      function a(e) {
        var t = 86400,
          n = 3600,
          i = 60,
          r = 1,
          a = Math.floor(e / t).toString(),
          s = Math.floor((e % t) / n).toString(),
          o = Math.floor((e % n) / i).toString(),
          c = Math.floor((e % i) / r).toString(),
          u = "";
        return (
          a > 0 && (u += a + "天"),
          s > 0 && (u += s + "小时"),
          o > 0 && (u += o + "分"),
          (u += c + "秒"),
          u
        );
      }
      function s(e) {
        return r()(new Date(1e3 * parseFloat(e))).format("YYYY-MM-DD HH:mm:ss");
      }
      function o(e, t, n) {
        return r()(new Date(1e3 * parseFloat(e)))
          .add(t, n)
          .format("YYYY-MM-DD HH:mm:ss");
      }
      function c(e, t, n) {
        return "" === e || "" === t || void 0 === e || void 0 === t
          ? Number.MAX_SAFE_INTEGER
          : ((e = r()(e, n)), (t = r()(t, n)), t.diff(e, "days"));
      }
    },
    d54a: function (e, t, n) {},
    d83c: function (e, t, n) {
      "use strict";
      n("d54a");
    },
    ffde: function (e, t, n) {},
  },
]);

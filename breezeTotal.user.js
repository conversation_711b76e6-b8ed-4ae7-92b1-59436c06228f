// ==UserScript==
// @name         清风审核量小时级查询
// @namespace    https://tampermonkey.net/
// @version      1.1
// @description  清风图片审核量级查询，支持每小时查询及日期范围查询 (快速版-移除并发控制)
// <AUTHOR>
// @match        https://breeze.opd.netease.com/*
// @connect      breeze.gameyw.netease.com
// @updateURL    https://gengxin.geluman.cn/OBMP/breezeTotal.user.js
// @downloadURL  https://gengxin.geluman.cn/OBMP/breezeTotal.user.js
// @grant        GM_xmlhttpRequest
// @grant        GM_addStyle
// ==/UserScript==
(function () {
  "use strict";
  // 添加全局样式
  GM_addStyle(`
        :root {
            --primary-color: #4361ee;
            --secondary-color: #3f37c9;
            --success-color: #4cc9f0;
            --text-color: #2b2d42;
            --light-gray: #f8f9fa;
            --medium-gray: #e9ecef;
            --dark-gray: #adb5bd;
            --border-radius: 8px;
            --box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            --transition: all 0.3s ease;
        }
        #audit-toggle-btn {
            position: fixed;
            left: 0;
            bottom: 0;
            background: var(--primary-color);
            color: white;
            font-size: 14px;
            padding: 5px 10px;
            border-radius: 0 var(--border-radius) 0 0;
            cursor: pointer;
            z-index: 10000;
            box-shadow: var(--box-shadow);
            font-family: 'Segoe UI', Roboto, 'Helvetica Neue', sans-serif;
            font-weight: 500;
            letter-spacing: 0.5px;
            transition: var(--transition);
            border: none;
            display: flex;
            align-items: center;
            gap: 6px;
        }
        #audit-toggle-btn:hover {
            background: var(--secondary-color);
        }
        #audit-toggle-btn svg {
            width: 16px;
            height: 16px;
            fill: currentColor;
        }
        #audit-panel {
            position: fixed;
            left: 10px;
            bottom: 50px;
            width: 360px;
            background: white;
            border-radius: var(--border-radius);
            box-shadow: var(--box-shadow);
            padding: 16px;
            font-size: 14px;
            color: var(--text-color);
            z-index: 10001;
            display: none;
            font-family: 'Segoe UI', Roboto, 'Helvetica Neue', sans-serif;
            border: 1px solid var(--medium-gray);
            transition: var(--transition);
        }
        .panel-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
            padding-bottom: 8px;
            border-bottom: 1px solid var(--medium-gray);
        }
        .panel-title {
            font-size: 16px;
            font-weight: 600;
            color: var(--text-color);
            margin: 0;
        }
        .close-btn {
            cursor: pointer;
            font-size: 18px;
            color: var(--dark-gray);
            transition: var(--transition);
            background: none;
            border: none;
            padding: 0;
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
        }
        .close-btn:hover {
            color: var(--text-color);
            background: var(--medium-gray);
        }
        .form-group {
            margin-bottom: 12px;
        }
        .form-label {
            display: block;
            margin-bottom: 6px;
            font-weight: 500;
            color: var(--text-color);
        }
        .form-input {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid var(--medium-gray);
            border-radius: var(--border-radius);
            font-size: 14px;
            transition: var(--transition);
            box-sizing: border-box;
        }
        .form-input:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 2px rgba(67, 97, 238, 0.2);
        }
        #userList {
            min-height: 60px;
            resize: vertical;
        }
        .btn {
            width: 100%;
            padding: 10px;
            border-radius: var(--border-radius);
            border: none;
            font-weight: 500;
            cursor: pointer;
            transition: var(--transition);
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 6px;
        }
        .btn-primary {
            background: var(--primary-color);
            color: white;
        }
        .btn-primary:hover {
            background: var(--secondary-color);
        }
        #result {
            margin-top: 16px;
            max-height: 300px;
            overflow-y: auto;
            font-size: 13px;
            border-radius: var(--border-radius);
            background: var(--light-gray);
            padding: 12px;
        }
        .user-result {
            margin-bottom: 12px;
            padding-bottom: 12px;
            border-bottom: 1px solid var(--medium-gray);
        }
        .user-result:last-child {
            margin-bottom: 0;
            padding-bottom: 0;
            border-bottom: none;
        }
        .user-name {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary-color);
        }
        .hour-item {
            display: flex;
            justify-content: space-between;
            padding: 4px 0;
        }
        .hour-time {
            color: var(--text-color);
        }
        .hour-count {
            font-weight: 500;
            color: var(--primary-color);
        }
        .no-data {
            color: var(--dark-gray);
            font-style: italic;
            text-align: center;
            padding: 8px;
        }
        .loading {
            display: inline-block;
            width: 16px;
            height: 16px;
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            border-top-color: white;
            animation: spin 1s ease-in-out infinite;
        }
        @keyframes spin {
            to { transform: rotate(360deg); }
        }
        .error-message {
            color: #e63946;
            padding: 8px;
            background: rgba(230, 57, 70, 0.1);
            border-radius: var(--border-radius);
            margin-top: 8px;
        }
        .success-message {
            color: #2a9d8f;
            padding: 8px;
            background: rgba(42, 157, 143, 0.1);
            border-radius: var(--border-radius);
            margin-top: 8px;
        }
        /* 进度条样式 */
        .progress-container {
            width: 100%;
            background-color: var(--medium-gray);
            border-radius: var(--border-radius);
            margin-top: 10px;
            overflow: hidden;
            display: none; /* 默认隐藏 */
        }
        .progress-bar {
            height: 10px;
            background-color: var(--primary-color);
            width: 0%;
            transition: width 0.3s ease;
        }
        .progress-text {
            font-size: 12px;
            text-align: center;
            margin-top: 5px;
            color: var(--text-color);
        }
        .date-range-group {
            display: flex;
            gap: 10px;
            align-items: end;
        }
        .date-range-group .form-group {
            flex: 1;
            margin-bottom: 0;
        }
    `);
  // ===== 贴边按钮 =====
  const toggleBtn = document.createElement("button");
  toggleBtn.id = "audit-toggle-btn";
  toggleBtn.innerHTML = `
        <svg viewBox="0 0 24 24">
            <path d="M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm0 16H5V5h14v14zM7 10h2v7H7zm4-3h2v10h-2zm4 6h2v4h-2z"/>
        </svg>
        审核查询
    `;
  document.body.appendChild(toggleBtn);
  // ===== 面板 =====
  const panel = document.createElement("div");
  panel.id = "audit-panel";
  panel.innerHTML = `
        <div class="panel-header">
            <h3 class="panel-title">审核量查询</h3>
            <button class="close-btn" id="close-panel">×</button>
        </div>
        <div class="form-group">
            <label class="form-label" for="userList">工号列表 (每行一个)</label>
            <textarea class="form-input" id="userList" placeholder="请输入工号，每行一个"></textarea>
        </div>
        <div class="date-range-group">
            <div class="form-group">
                <label class="form-label" for="queryStartDate">开始日期</label>
                <input class="form-input" type="date" id="queryStartDate">
            </div>
            <div class="form-group">
                <label class="form-label" for="queryDate">结束日期</label>
                <input class="form-input" type="date" id="queryDate">
            </div>
        </div>
        <div class="form-group">
            <label class="form-label" for="queryRange">快捷范围</label>
            <select class="form-input" id="queryRange">
                <option value="today">今日</option>
                <option value="7">近7天</option>
                <option value="30">近30天</option>
            </select>
        </div>
        <button id="queryBtn" class="btn btn-primary">
            <span id="btn-text">查询</span>
            <span id="btn-loader" class="loading" style="display:none;"></span>
        </button>
        <!-- 进度条 -->
        <div id="progress-container" class="progress-container">
            <div id="progress-bar" class="progress-bar"></div>
        </div>
        <div id="progress-text" class="progress-text"></div>
        <div id="result"></div>
    `;
  document.body.appendChild(panel);
  // ===== 交互逻辑 =====
  toggleBtn.addEventListener("click", () => {
    panel.style.display = panel.style.display === "none" ? "block" : "none";
  });
  panel.querySelector("#close-panel").addEventListener("click", () => {
    panel.style.display = "none";
  });

  // 设置默认日期为今天
  const today = new Date();
  const todayStr = today.toLocaleDateString("en-CA");
  const startDateInput = panel.querySelector("#queryStartDate");
  const endDateInput = panel.querySelector("#queryDate");
  startDateInput.value = todayStr;
  endDateInput.value = todayStr;

  // 快捷范围选择器逻辑
  const rangeSelect = panel.querySelector("#queryRange");
  rangeSelect.addEventListener("change", function () {
    const days = this.value;
    if (days === "today") {
      // 如果选择“今日”，直接设置为当天
      startDateInput.value = todayStr;
      endDateInput.value = todayStr;
    } else if (days) {
      // 如果选择了“近7天”或“近30天”
      const endDate = new Date(todayStr);
      const startDate = new Date(endDate);
      startDate.setDate(startDate.getDate() - parseInt(days) + 1);

      const startDateStr = startDate.toLocaleDateString("en-CA");
      startDateInput.value = startDateStr;
      endDateInput.value = todayStr; // 结束日期固定为今天
    }
  });

  // 默认选中“今日”
  rangeSelect.value = "today";

  // ===== 查询逻辑 =====
  panel.querySelector("#queryBtn").addEventListener("click", async function () {
    const userRaw = panel.querySelector("#userList").value.trim();
    const startDateStr = startDateInput.value;
    const endDateStr = endDateInput.value;
    const resultDiv = panel.querySelector("#result");
    const btnText = panel.querySelector("#btn-text");
    const btnLoader = panel.querySelector("#btn-loader");
    const progressContainer = panel.querySelector("#progress-container");
    const progressBar = panel.querySelector("#progress-bar");
    const progressText = panel.querySelector("#progress-text");

    if (!userRaw || !startDateStr || !endDateStr) {
      resultDiv.innerHTML = `<div class="error-message">❌ 请输入工号和日期范围</div>`;
      return;
    }

    const start = new Date(startDateStr);
    const end = new Date(endDateStr);
    if (start > end) {
      resultDiv.innerHTML = `<div class="error-message">❌ 开始日期不能晚于结束日期</div>`;
      return;
    }

    // 显示加载状态和进度条
    btnText.textContent = "查询中...";
    btnLoader.style.display = "inline-block";
    this.disabled = true;
    progressContainer.style.display = "block";
    progressBar.style.width = "0%";
    progressText.textContent = "准备查询...";

    const users = userRaw
      .split(/\r?\n/)
      .map((u) => u.trim())
      .filter(Boolean);
    resultDiv.innerHTML = "";

    try {
      const allResults = [];
      for (const user of users) {
        // 调用新的多日范围查询函数，并传入进度更新回调
        const dailyData = await queryUserDailyRange(
          user,
          startDateStr,
          endDateStr,
          (current, total) => {
            const progressPercent = Math.round((current / total) * 100);
            progressBar.style.width = `${progressPercent}%`;
            progressText.textContent = `正在查询 ${user}: ${current}/${total} (${progressPercent}%)`;
          }
        );
        allResults.push({ user, dailyData });
      }
      displayResultMultiDay(allResults, startDateStr, endDateStr);
      progressText.textContent = "查询完成!";
    } catch (e) {
      resultDiv.innerHTML = `<div class="error-message">❌ 查询失败: ${e.message}</div>`;
      progressText.textContent = "查询失败!";
    } finally {
      // 恢复按钮状态和隐藏进度条
      setTimeout(() => {
        // 稍微延迟隐藏，让用户看到完成状态
        btnText.textContent = "查询";
        btnLoader.style.display = "none";
        this.disabled = false;
        progressContainer.style.display = "none";
        progressText.textContent = "";
      }, 500);
    }
  });

  function getCreateTimeRange(dateStr) {
    const d = new Date(dateStr);
    d.setDate(d.getDate() - 1);
    const prev = d.toISOString().slice(0, 10);
    return {
      createStart: `${prev} 00:00:00`,
      createEnd: `${dateStr} 23:59:59`,
    };
  }

  // 修改：多日范围查询函数 (移除并发控制)
  async function queryUserDailyRange(
    username,
    startDateStr,
    endDateStr,
    onProgress
  ) {
    const dailyData = [];
    const start = new Date(startDateStr);
    const end = new Date(endDateStr);

    // 生成日期范围内的所有日期
    const dates = [];
    const currentDate = new Date(start);
    while (currentDate <= end) {
      dates.push(new Date(currentDate)); // 复制日期对象
      currentDate.setDate(currentDate.getDate() + 1);
    }

    let completedTasks = 0;
    const totalTasks = dates.length;

    // 为每一天创建查询任务 (使用 Promise.all 直接并行)
    const dailyPromises = dates.map(async (dateObj) => {
      const currentDateString = dateObj.toLocaleDateString("en-CA");
      try {
        const hourly = await queryUserHourly(username, currentDateString); // 调用原始的并行小时查询
        // 更新进度
        completedTasks++;
        onProgress(completedTasks, totalTasks);
        return { date: currentDateString, hourly };
      } catch (err) {
        console.error(
          `查询 ${username} 在 ${currentDateString} 的数据失败:`,
          err
        );
        completedTasks++;
        onProgress(completedTasks, totalTasks);
        return { date: currentDateString, hourly: [], error: err.message };
      }
    });

    // 直接并行执行所有日期的查询 (移除 concurrentLimit)
    const results = await Promise.all(dailyPromises);
    return results;
  }

  // 原始的单日小时查询函数 (保持不变，内部使用 Promise.all 并行)
  async function queryUserHourly(username, date) {
    const { createStart, createEnd } = getCreateTimeRange(date);
    const hourly = [];
    // 使用Promise.all并行查询所有小时段 (原始方式)
    const promises = [];
    for (let h = 0; h < 24; h++) {
      const closeStart = `${date} ${String(h).padStart(2, "0")}:00:00`;
      const closeEnd = `${date} ${String(h).padStart(2, "0")}:59:59`;
      promises.push(
        fetchCount(username, createStart, createEnd, closeStart, closeEnd)
      );
    }
    const results = await Promise.all(promises); // 原始脚本的并行方式
    return results.map((count, h) => ({ hour: h, count }));
  }

  // 修改：fetchCount 函数 (移除重试和超时)
  function fetchCount(username, createStart, createEnd, closeStart, closeEnd) {
    return new Promise((resolve, reject) => {
      // 移除 attempts 计数和重试逻辑
      // 移除 timeout 设置

      GM_xmlhttpRequest({
        method: "GET",
        url: `https://breeze.gameyw.netease.com/api/cms/issue/list?pageNum=1&pageSize=1&createTimeStart=${encodeURIComponent(
          createStart
        )}&createTimeEnd=${encodeURIComponent(
          createEnd
        )}&closeTimeStart=${encodeURIComponent(
          closeStart
        )}&closeTimeEnd=${encodeURIComponent(
          closeEnd
        )}&handleUsername=${username}&cold=false`,
        cookie: true,
        // 移除 timeout: 10000,
        onload: (res) => {
          if (res.status === 200) {
            try {
              resolve(JSON.parse(res.responseText)?.data?.total ?? 0);
            } catch (e) {
              // 移除重试逻辑，直接 reject
              reject(new Error("解析失败"));
            }
          } else {
            // 移除重试逻辑，直接 reject
            reject(new Error(`HTTP ${res.status}`));
          }
        },
        onerror: (err) => {
          // 移除重试逻辑，直接 reject
          reject(new Error("网络错误"));
        },
        // 移除 ontimeout 处理
      });
    });
  }

  // 修改或新增：适应多日查询结果的展示函数
  function displayResultMultiDay(allUsersData, startDateStr, endDateStr) {
    const resultDiv = panel.querySelector("#result");
    if (allUsersData.length === 0) {
      resultDiv.innerHTML = `<div class="no-data">无查询结果</div>`;
      return;
    }

    let html = "";

    // 添加总览信息
    html += `<div class="success-message">✅ 查询成功 (${startDateStr} 至 ${endDateStr})</div>`;

    allUsersData.forEach(({ user, dailyData }) => {
      html += `<div class="user-result">`;
      // 计算用户总合计
      let userTotalCount = 0;
      dailyData.forEach((day) => {
        if (!day.error) {
          userTotalCount += day.hourly.reduce((sum, h) => sum + h.count, 0);
        }
      });
      html += `<div class="user-name">${user} <small>(合计: ${userTotalCount}条)</small></div>`;

      let userHasAnyData = false;

      dailyData.forEach((day) => {
        if (day.error) {
          html += `<div class="error-message">❌ ${day.date} 查询失败: ${day.error}</div>`;
          return; // 跳过这一天的展示
        }

        const dayTotal = day.hourly.reduce((sum, h) => sum + h.count, 0);

        if (dayTotal > 0) {
          userHasAnyData = true;
          // 添加日期标题
          html += `<div style="font-weight: 500; margin-top: 8px;">${day.date} (总计: ${dayTotal}条)</div>`;
          day.hourly
            .filter((h) => h.count > 0)
            .forEach((h) => {
              html += `
                            <div class="hour-item">
                                <span class="hour-time">${String(
                                  h.hour
                                ).padStart(2, "0")}:00–${String(
                h.hour + 1
              ).padStart(2, "0")}:00</span>
                                <span class="hour-count">${h.count}条</span>
                            </div>
                        `;
            });
        }
      });

      if (!userHasAnyData) {
        html += `<div class="no-data">所选日期范围内无审核记录</div>`;
      }

      html += `</div>`; // Close user-result
    });

    resultDiv.innerHTML = html;
  }
})();
